# 币安量化交易系统 - 安装和配置指南

## 🚀 快速开始

### 1. 环境准备

确保您已经安装了以下软件：
- Python 3.11+
- Conda 或 Miniconda

### 2. 创建虚拟环境

```bash
# 创建conda环境
conda create -n quant python=3.11 -y
conda activate quant

# 安装依赖包
pip install -r requirements.txt
conda install -c conda-forge ta-lib -y
```

### 3. 配置API密钥

#### 3.1 获取币安API密钥

1. 登录 [币安官网](https://www.binance.com)
2. 进入 "API管理" 页面
3. 创建新的API密钥
4. **重要**: 设置API权限为 "现货交易" 和 "读取信息"
5. **建议**: 首次使用时启用 "测试网络" 模式

#### 3.2 配置环境变量

```bash
# 复制配置文件
cp config/.env.example config/.env

# 编辑配置文件
nano config/.env
```

在 `.env` 文件中填入您的API信息：

```env
# 币安API配置
BINANCE_API_KEY=your_actual_api_key_here
BINANCE_SECRET_KEY=your_actual_secret_key_here

# 是否使用测试网络 (建议初次使用时设为True)
BINANCE_TESTNET=True

# 其他配置保持默认即可
```

### 4. 测试系统

```bash
# 激活环境
conda activate quant

# 运行系统测试
python test_system.py
```

如果看到类似以下输出，说明系统配置成功：

```
总计: 5/5 个测试通过
🎉 所有测试通过！系统可以正常运行。
```

### 5. 启动交易系统

```bash
# 启动主程序
python main.py
```

## 📊 策略配置

### DOJIplus趋势策略

基于十字星形态和多个技术指标的趋势跟踪策略：

```python
DOJI_STRATEGY_CONFIG = {
    'timeframe': '1h',          # 时间框架
    'rsi_period': 14,           # RSI周期
    'rsi_overbought': 70,       # RSI超买线
    'rsi_oversold': 30,         # RSI超卖线
    'ma_period': 20,            # 移动平均线周期
    'stop_loss_pct': 0.02,      # 止损百分比 2%
    'take_profit_pct': 0.04     # 止盈百分比 4%
}
```

### Triangle Theme三角套利

监控三个货币对之间的价格差异：

```python
TRIANGLE_ARBITRAGE_CONFIG = {
    'min_profit_threshold': 0.001,  # 最小利润阈值 0.1%
    'max_slippage': 0.002,          # 最大滑点 0.2%
    'check_interval': 5,            # 检查间隔(秒)
    'triangles': [
        ['BTC', 'ETH', 'USDT'],
        ['BTC', 'SOL', 'USDT'],
        ['ETH', 'SOL', 'USDT']
    ]
}
```

### MatrixHFGT网格交易

在价格区间内进行高频网格交易：

```python
GRID_TRADING_CONFIG = {
    'grid_count': 10,               # 网格数量
    'price_range_pct': 0.1,         # 价格范围百分比 10%
    'order_amount_pct': 0.1,        # 每单金额占总资金比例
    'rebalance_threshold': 0.05,    # 重新平衡阈值
    'max_orders': 20                # 最大订单数
}
```

## ⚠️ 风险管理

系统内置了完善的风险管理机制：

### 风险限制

- **最大日损失**: 5% (可配置)
- **最大回撤**: 10% (可配置)
- **单仓位限制**: 20% (可配置)
- **总敞口限制**: 80% (可配置)

### 紧急停止

系统会在以下情况自动停止交易：
- 日损失超过限制的1.5倍
- 回撤超过限制的1.2倍
- API连接异常
- 账户状态异常

## 📈 监控和日志

### 日志文件

- `logs/trading.log` - 主日志文件
- `logs/trades.log` - 交易专用日志

### 监控指标

系统实时监控以下指标：
- 账户余额变化
- 策略表现
- 风险指标
- 系统状态

## 🔧 高级配置

### 自定义策略参数

编辑 `config/config.py` 文件中的策略配置：

```python
# 修改DOJIplus策略参数
DOJI_STRATEGY_CONFIG = {
    'timeframe': '4h',          # 改为4小时时间框架
    'rsi_period': 21,           # 改为21周期RSI
    'stop_loss_pct': 0.015,     # 改为1.5%止损
    # ... 其他参数
}
```

### 添加新的交易对

在 `config/config.py` 中修改：

```python
TRADING_PAIRS = [
    'BTCUSDT',
    'ETHUSDT', 
    'SOLUSDT',
    'ADAUSDT',  # 添加新的交易对
    'DOTUSDT'   # 添加新的交易对
]
```

## 🚨 重要提醒

1. **测试环境**: 强烈建议先在测试网络中运行和测试
2. **资金管理**: 不要投入超过您承受能力的资金
3. **监控**: 定期检查系统运行状态和交易结果
4. **备份**: 定期备份配置文件和日志
5. **更新**: 关注币安API的更新和变化

## 📞 故障排除

### 常见问题

**Q: API连接失败**
A: 检查API密钥是否正确，网络连接是否正常，是否启用了正确的权限

**Q: 策略不生成信号**
A: 检查市场条件是否满足策略要求，调整策略参数或时间框架

**Q: 系统频繁报错**
A: 检查日志文件，确认网络连接稳定，API限制是否触发

**Q: 回测结果与实盘不符**
A: 考虑滑点、手续费、网络延迟等实际交易成本

### 获取帮助

如果遇到问题，请：
1. 查看日志文件中的错误信息
2. 运行 `python test_system.py` 诊断问题
3. 检查网络连接和API配置
4. 参考币安API文档

## 📄 许可证

本项目采用 MIT 许可证。请在使用前仔细阅读许可证条款。

---

**免责声明**: 本软件仅供学习和研究使用。量化交易存在风险，请谨慎使用，作者不承担任何投资损失责任。
