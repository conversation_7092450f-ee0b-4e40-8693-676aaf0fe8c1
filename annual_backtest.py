#!/usr/bin/env python3
"""
年度回测脚本
对三种策略进行2024-07-01到2025-06-30的完整回测
每种策略使用1万USDC本金，最终以USDC计算收益
"""

import sys
import os
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.api.binance_client import BinanceClient
from src.data.cached_data_manager import CachedDataManager
from src.strategies.doji_plus import DojiPlusStrategy
from src.strategies.triangle_arbitrage import TriangleArbitrageStrategy
from src.strategies.matrix_grid import MatrixGridStrategy
from src.backtest.backtester import Backtester, BacktestResult
from config.config import config
from src.utils.logger import get_logger

logger = get_logger(__name__)

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")


class AnnualBacktester:
    """年度回测器"""
    
    def __init__(self):
        """初始化年度回测器"""
        self.client = BinanceClient()
        self.cache_manager = CachedDataManager(self.client)
        
        # 回测参数
        self.start_date = '2024-07-01'
        self.end_date = '2025-06-30'
        self.initial_balance = 10000.0  # 1万USDC
        
        # 策略配置
        self.strategies_config = {
            'DOJIplus': {
                'class': DojiPlusStrategy,
                'config': config.get_strategy_config('doji_plus'),
                'timeframe': '1h',
                'description': 'DOJIplus趋势策略 - 基于十字星形态和多技术指标'
            },
            'TriangleArbitrage': {
                'class': TriangleArbitrageStrategy,
                'config': config.get_strategy_config('triangle_arbitrage'),
                'timeframe': '5m',
                'description': '三角套利策略 - BTC/ETH/SOL三角套利'
            },
            'MatrixGrid': {
                'class': MatrixGridStrategy,
                'config': config.get_strategy_config('grid_trading'),
                'timeframe': '15m',
                'description': '网格交易策略 - 动态网格高频交易'
            }
        }
        
        # 测试交易对 (使用USDT，最终转换为USDC等值)
        self.test_symbols = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT']
        
        logger.info("年度回测器初始化完成")
    
    def preload_data(self):
        """预加载所有回测数据"""
        print("=" * 80)
        print("预加载回测数据")
        print("=" * 80)
        
        # 收集所有需要的时间框架
        timeframes = list(set([config['timeframe'] for config in self.strategies_config.values()]))
        
        print(f"回测期间: {self.start_date} 到 {self.end_date}")
        print(f"交易对: {self.test_symbols}")
        print(f"时间框架: {timeframes}")
        print(f"策略数量: {len(self.strategies_config)}")
        
        # 预加载数据
        self.cached_data = self.cache_manager.preload_backtest_data(
            symbols=self.test_symbols,
            timeframes=timeframes,
            start_date=self.start_date,
            end_date=self.end_date
        )
        
        # 显示数据统计
        print("\n数据加载统计:")
        for symbol in self.test_symbols:
            for timeframe in timeframes:
                data_len = len(self.cached_data[symbol][timeframe])
                print(f"  {symbol} {timeframe}: {data_len} 条记录")
        
        # 显示缓存信息
        cache_info = self.cache_manager.get_cache_info()
        print(f"\n缓存信息:")
        print(f"  缓存文件: {cache_info['total_files']} 个")
        print(f"  缓存大小: {cache_info['total_size_mb']:.2f} MB")
        
        print("✓ 数据预加载完成")
    
    def run_strategy_backtest(self, strategy_name: str, strategy_config: Dict) -> Dict[str, BacktestResult]:
        """运行单个策略的回测"""
        print(f"\n{'='*60}")
        print(f"回测策略: {strategy_name}")
        print(f"描述: {strategy_config['description']}")
        print(f"时间框架: {strategy_config['timeframe']}")
        print(f"{'='*60}")
        
        results = {}
        
        try:
            # 创建策略实例
            strategy = strategy_config['class'](
                self.client,
                self.cache_manager.data_fetcher,
                strategy_config['config']
            )
            
            # 创建回测器
            backtester = Backtester(self.cache_manager.data_fetcher, initial_balance=self.initial_balance)
            
            # 对每个交易对进行回测
            for symbol in self.test_symbols:
                print(f"\n--- 回测交易对: {symbol} ---")
                
                try:
                    # 获取缓存数据
                    timeframe = strategy_config['timeframe']
                    data = self.cached_data[symbol][timeframe]
                    
                    if data.empty:
                        print(f"  ✗ 无数据可用")
                        continue
                    
                    print(f"  数据范围: {data.index[0]} 到 {data.index[-1]}")
                    print(f"  数据条数: {len(data)}")
                    
                    # 运行回测
                    result = backtester.run_backtest_with_data(
                        strategy=strategy,
                        symbol=symbol,
                        data=data
                    )
                    
                    results[symbol] = result
                    
                    # 显示结果
                    print(f"  ✓ 回测完成")
                    print(f"    初始资金: ${result.initial_balance:,.2f} USDC")
                    print(f"    最终资金: ${result.final_balance:,.2f} USDC")
                    print(f"    总收益: ${result.total_return:,.2f} USDC ({result.total_return_pct:.2%})")
                    print(f"    交易次数: {result.total_trades}")
                    print(f"    胜率: {result.win_rate:.2%}")
                    print(f"    最大回撤: {result.max_drawdown:.2%}")
                    print(f"    夏普比率: {result.sharpe_ratio:.2f}")
                    
                except Exception as e:
                    print(f"  ✗ 回测失败: {e}")
                    logger.error(f"策略回测失败 {strategy_name}-{symbol}: {e}")
            
            print(f"\n✓ {strategy_name} 策略回测完成")
            
        except Exception as e:
            print(f"✗ {strategy_name} 策略初始化失败: {e}")
            logger.error(f"策略初始化失败 {strategy_name}: {e}")
        
        return results
    
    def run_annual_backtest(self) -> Dict[str, Dict[str, BacktestResult]]:
        """运行年度回测"""
        print("=" * 80)
        print("年度策略回测分析")
        print(f"回测期间: {self.start_date} 到 {self.end_date}")
        print(f"初始资金: ${self.initial_balance:,.2f} USDC (每策略)")
        print("=" * 80)
        
        # 预加载数据
        self.preload_data()
        
        # 运行所有策略回测
        all_results = {}
        
        for strategy_name, strategy_config in self.strategies_config.items():
            results = self.run_strategy_backtest(strategy_name, strategy_config)
            all_results[strategy_name] = results
        
        return all_results
    
    def generate_annual_report(self, all_results: Dict[str, Dict[str, BacktestResult]]):
        """生成年度回测报告"""
        print("\n" + "=" * 80)
        print("年度回测报告")
        print("=" * 80)
        
        # 策略汇总表
        print(f"\n📊 策略收益汇总 (初始资金: ${self.initial_balance:,.2f} USDC)")
        print("-" * 100)
        print(f"{'策略':<15} {'交易对':<10} {'最终资金':<12} {'总收益':<12} {'收益率':<10} "
              f"{'交易次数':<8} {'胜率':<8} {'最大回撤':<10} {'夏普比率':<10}")
        print("-" * 100)
        
        strategy_summary = {}
        
        for strategy_name, strategy_results in all_results.items():
            strategy_metrics = []
            
            for symbol, result in strategy_results.items():
                if result:
                    print(f"{strategy_name:<15} {symbol:<10} "
                          f"${result.final_balance:<11,.2f} "
                          f"${result.total_return:<11,.2f} "
                          f"{result.total_return_pct:<10.2%} "
                          f"{result.total_trades:<8} "
                          f"{result.win_rate:<8.2%} "
                          f"{result.max_drawdown:<10.2%} "
                          f"{result.sharpe_ratio:<10.2f}")
                    
                    strategy_metrics.append({
                        'final_balance': result.final_balance,
                        'total_return': result.total_return,
                        'return_pct': result.total_return_pct,
                        'trades': result.total_trades,
                        'win_rate': result.win_rate,
                        'max_drawdown': result.max_drawdown,
                        'sharpe_ratio': result.sharpe_ratio
                    })
            
            # 计算策略平均表现
            if strategy_metrics:
                avg_final_balance = np.mean([m['final_balance'] for m in strategy_metrics])
                avg_return = np.mean([m['total_return'] for m in strategy_metrics])
                avg_return_pct = np.mean([m['return_pct'] for m in strategy_metrics])
                total_trades = sum([m['trades'] for m in strategy_metrics])
                avg_win_rate = np.mean([m['win_rate'] for m in strategy_metrics])
                avg_drawdown = np.mean([m['max_drawdown'] for m in strategy_metrics])
                avg_sharpe = np.mean([m['sharpe_ratio'] for m in strategy_metrics])
                
                strategy_summary[strategy_name] = {
                    'avg_final_balance': avg_final_balance,
                    'avg_return': avg_return,
                    'avg_return_pct': avg_return_pct,
                    'total_trades': total_trades,
                    'avg_win_rate': avg_win_rate,
                    'avg_drawdown': avg_drawdown,
                    'avg_sharpe': avg_sharpe,
                    'metrics': strategy_metrics
                }
        
        # 策略排名
        print("\n🏆 策略年度排名")
        print("-" * 60)
        
        # 按平均收益率排名
        sorted_by_return = sorted(strategy_summary.items(), 
                                 key=lambda x: x[1]['avg_return_pct'], reverse=True)
        print("按平均收益率排名:")
        for i, (name, metrics) in enumerate(sorted_by_return, 1):
            print(f"  {i}. {name}: {metrics['avg_return_pct']:.2%} "
                  f"(${metrics['avg_return']:,.2f} USDC)")
        
        # 按夏普比率排名
        sorted_by_sharpe = sorted(strategy_summary.items(), 
                                 key=lambda x: x[1]['avg_sharpe'], reverse=True)
        print("\n按夏普比率排名:")
        for i, (name, metrics) in enumerate(sorted_by_sharpe, 1):
            print(f"  {i}. {name}: {metrics['avg_sharpe']:.2f}")
        
        # 风险分析
        print("\n⚠️  风险分析")
        print("-" * 60)
        for name, metrics in strategy_summary.items():
            risk_level = "低" if metrics['avg_drawdown'] < 0.05 else "中" if metrics['avg_drawdown'] < 0.15 else "高"
            print(f"{name}: 平均最大回撤 {metrics['avg_drawdown']:.2%} (风险等级: {risk_level})")
        
        # 保存详细报告
        self.save_annual_report(all_results, strategy_summary)

        # 生成图表
        self.create_annual_charts(strategy_summary)

        return strategy_summary

    def save_annual_report(self, all_results: Dict[str, Dict[str, BacktestResult]],
                          strategy_summary: Dict[str, Any]):
        """保存年度报告到文件"""
        try:
            # 准备报告数据
            report_data = {
                'metadata': {
                    'backtest_period': f"{self.start_date} 到 {self.end_date}",
                    'initial_balance_per_strategy': self.initial_balance,
                    'test_symbols': self.test_symbols,
                    'strategies': list(self.strategies_config.keys()),
                    'generated_at': datetime.now().isoformat()
                },
                'strategy_summary': {},
                'detailed_results': {}
            }

            # 添加策略汇总
            for strategy_name, metrics in strategy_summary.items():
                report_data['strategy_summary'][strategy_name] = {
                    'avg_final_balance': metrics['avg_final_balance'],
                    'avg_return_usdc': metrics['avg_return'],
                    'avg_return_percentage': metrics['avg_return_pct'],
                    'total_trades': metrics['total_trades'],
                    'avg_win_rate': metrics['avg_win_rate'],
                    'avg_max_drawdown': metrics['avg_drawdown'],
                    'avg_sharpe_ratio': metrics['avg_sharpe']
                }

            # 添加详细结果
            for strategy_name, strategy_results in all_results.items():
                report_data['detailed_results'][strategy_name] = {}
                for symbol, result in strategy_results.items():
                    if result:
                        report_data['detailed_results'][strategy_name][symbol] = {
                            'initial_balance': result.initial_balance,
                            'final_balance': result.final_balance,
                            'total_return': result.total_return,
                            'total_return_pct': result.total_return_pct,
                            'total_trades': result.total_trades,
                            'winning_trades': result.winning_trades,
                            'losing_trades': result.losing_trades,
                            'win_rate': result.win_rate,
                            'max_drawdown': result.max_drawdown,
                            'sharpe_ratio': result.sharpe_ratio,
                            'profit_factor': getattr(result, 'profit_factor', 0)
                        }

            # 保存到文件
            with open('annual_backtest_report.json', 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2, default=str)

            print(f"\n📄 详细报告已保存到: annual_backtest_report.json")

        except Exception as e:
            print(f"保存报告失败: {e}")
            logger.error(f"保存年度报告失败: {e}")

    def create_annual_charts(self, strategy_summary: Dict[str, Any]):
        """创建年度回测图表"""
        try:
            # 创建图表
            fig, axes = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle(f'年度策略回测分析 ({self.start_date} 到 {self.end_date})',
                        fontsize=16, fontweight='bold')

            strategies = list(strategy_summary.keys())

            # 1. 平均收益率对比
            returns = [strategy_summary[s]['avg_return_pct'] * 100 for s in strategies]
            colors = ['#1f77b4', '#ff7f0e', '#2ca02c']
            bars1 = axes[0, 0].bar(strategies, returns, color=colors)
            axes[0, 0].set_title('平均收益率对比 (%)')
            axes[0, 0].set_ylabel('收益率 (%)')
            axes[0, 0].tick_params(axis='x', rotation=45)

            # 添加数值标签
            for bar, value in zip(bars1, returns):
                axes[0, 0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                               f'{value:.1f}%', ha='center', va='bottom')

            # 2. 平均收益金额对比 (USDC)
            returns_usdc = [strategy_summary[s]['avg_return'] for s in strategies]
            bars2 = axes[0, 1].bar(strategies, returns_usdc, color=['#d62728', '#9467bd', '#8c564b'])
            axes[0, 1].set_title('平均收益金额对比 (USDC)')
            axes[0, 1].set_ylabel('收益 (USDC)')
            axes[0, 1].tick_params(axis='x', rotation=45)

            # 添加数值标签
            for bar, value in zip(bars2, returns_usdc):
                axes[0, 1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 10,
                               f'${value:,.0f}', ha='center', va='bottom')

            # 3. 最大回撤对比
            drawdowns = [strategy_summary[s]['avg_drawdown'] * 100 for s in strategies]
            bars3 = axes[1, 0].bar(strategies, drawdowns, color=['#e377c2', '#7f7f7f', '#bcbd22'])
            axes[1, 0].set_title('平均最大回撤对比 (%)')
            axes[1, 0].set_ylabel('最大回撤 (%)')
            axes[1, 0].tick_params(axis='x', rotation=45)

            # 添加数值标签
            for bar, value in zip(bars3, drawdowns):
                axes[1, 0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                               f'{value:.1f}%', ha='center', va='bottom')

            # 4. 夏普比率对比
            sharpe_ratios = [strategy_summary[s]['avg_sharpe'] for s in strategies]
            bars4 = axes[1, 1].bar(strategies, sharpe_ratios, color=['#17becf', '#ff9896', '#98df8a'])
            axes[1, 1].set_title('平均夏普比率对比')
            axes[1, 1].set_ylabel('夏普比率')
            axes[1, 1].tick_params(axis='x', rotation=45)

            # 添加数值标签
            for bar, value in zip(bars4, sharpe_ratios):
                axes[1, 1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.02,
                               f'{value:.2f}', ha='center', va='bottom')

            plt.tight_layout()

            # 保存图表
            chart_path = 'annual_backtest_analysis.png'
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            print(f"📈 年度分析图表已保存到: {chart_path}")

            plt.close()

        except Exception as e:
            print(f"创建图表失败: {e}")
            logger.error(f"创建年度图表失败: {e}")


def main():
    """主函数"""
    print("🚀 年度策略回测系统")
    print("=" * 80)
    
    try:
        # 创建回测器
        backtester = AnnualBacktester()
        
        # 运行年度回测
        all_results = backtester.run_annual_backtest()
        
        # 生成报告
        strategy_summary = backtester.generate_annual_report(all_results)
        
        print("\n🎉 年度回测完成！")
        print("详细报告已保存到 annual_backtest_report.json")
        
    except Exception as e:
        print(f"❌ 年度回测失败: {e}")
        logger.error(f"年度回测失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
