#!/usr/bin/env python3
"""
系统测试脚本
测试各个模块的基本功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.config import config
from src.utils.logger import get_logger
from src.api.binance_client import BinanceClient
from src.data.data_fetcher import DataFetcher
from src.data.indicators import TechnicalIndicators

logger = get_logger(__name__)


def test_config():
    """测试配置"""
    print("=" * 50)
    print("测试配置模块")
    print("=" * 50)
    
    print(f"交易对: {config.TRADING_PAIRS}")
    print(f"启用的策略: {config.get_enabled_strategies()}")
    print(f"测试网络: {config.BINANCE_TESTNET}")
    print(f"API密钥配置: {'已配置' if config.BINANCE_API_KEY else '未配置'}")
    
    return True


def test_binance_client():
    """测试币安客户端"""
    print("=" * 50)
    print("测试币安API客户端")
    print("=" * 50)
    
    try:
        # 创建客户端
        client = BinanceClient()
        print("✓ 客户端创建成功")
        
        # 测试连接
        if client.test_connection():
            print("✓ API连接测试成功")
        else:
            print("✗ API连接测试失败")
            return False
        
        # 获取账户信息
        account_info = client.get_account_info()
        print(f"✓ 账户信息获取成功")
        print(f"  - 账户类型: {account_info['account_type']}")
        print(f"  - 可交易: {account_info['can_trade']}")
        print(f"  - 总余额: {account_info['total_balance_usdt']:.2f} USDT")
        
        # 获取交易对信息
        symbol_info = client.get_symbol_info('BTCUSDT')
        print(f"✓ 交易对信息获取成功: {symbol_info['symbol']}")
        
        # 获取价格
        price = client.get_ticker_price('BTCUSDT')
        print(f"✓ 价格获取成功: BTC = {price:.2f} USDT")
        
        return True
        
    except Exception as e:
        print(f"✗ 币安客户端测试失败: {e}")
        return False


def test_data_fetcher():
    """测试数据获取器"""
    print("=" * 50)
    print("测试数据获取器")
    print("=" * 50)
    
    try:
        client = BinanceClient()
        data_fetcher = DataFetcher(client)
        print("✓ 数据获取器创建成功")
        
        # 测试实时价格获取
        price = data_fetcher.get_realtime_price('BTCUSDT')
        print(f"✓ 实时价格获取成功: BTC = {price:.2f} USDT")
        
        # 测试批量价格获取
        prices = data_fetcher.get_realtime_prices(['BTCUSDT', 'ETHUSDT'])
        print(f"✓ 批量价格获取成功: {len(prices)} 个价格")
        
        # 测试历史数据获取
        data = data_fetcher.get_historical_data('BTCUSDT', '1h', days=7)
        print(f"✓ 历史数据获取成功: {len(data)} 条K线数据")
        
        # 测试订单簿数据
        orderbook = data_fetcher.get_orderbook_data('BTCUSDT')
        print(f"✓ 订单簿数据获取成功: {len(orderbook['bids'])} 买单, {len(orderbook['asks'])} 卖单")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据获取器测试失败: {e}")
        return False


def test_technical_indicators():
    """测试技术指标"""
    print("=" * 50)
    print("测试技术指标")
    print("=" * 50)
    
    try:
        client = BinanceClient()
        data_fetcher = DataFetcher(client)
        
        # 获取测试数据
        data = data_fetcher.get_historical_data('BTCUSDT', '1h', days=30)
        print(f"✓ 获取测试数据: {len(data)} 条")
        
        # 计算技术指标
        indicators = TechnicalIndicators.calculate_all_indicators(data)
        print(f"✓ 技术指标计算完成: {len(indicators.columns)} 个指标")
        
        # 显示最新指标值
        latest = indicators.iloc[-1]
        print(f"  - RSI: {latest['rsi']:.2f}")
        print(f"  - MACD: {latest['macd']:.6f}")
        print(f"  - SMA(20): {latest['sma_20']:.2f}")
        print(f"  - 布林带上轨: {latest['bb_upper']:.2f}")
        print(f"  - ATR: {latest['atr']:.2f}")
        
        return True
        
    except Exception as e:
        print(f"✗ 技术指标测试失败: {e}")
        return False


def test_strategies():
    """测试策略"""
    print("=" * 50)
    print("测试交易策略")
    print("=" * 50)
    
    try:
        from src.strategies.doji_plus import DojiPlusStrategy
        
        client = BinanceClient()
        data_fetcher = DataFetcher(client)
        
        # 测试DOJIplus策略
        doji_config = config.get_strategy_config('doji_plus')
        doji_strategy = DojiPlusStrategy(client, data_fetcher, doji_config)
        print("✓ DOJIplus策略创建成功")
        
        # 获取测试数据
        data = data_fetcher.get_historical_data('BTCUSDT', '1h', days=30)
        
        # 生成信号
        signals = doji_strategy.generate_signals('BTCUSDT', data)
        print(f"✓ 信号生成成功: {len(signals)} 个信号")
        
        # 获取策略状态
        status = doji_strategy.get_strategy_status('BTCUSDT')
        print(f"✓ 策略状态获取成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 策略测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始系统测试...")
    print()
    
    tests = [
        ("配置模块", test_config),
        ("币安客户端", test_binance_client),
        ("数据获取器", test_data_fetcher),
        ("技术指标", test_technical_indicators),
        ("交易策略", test_strategies),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print()
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
            results.append((test_name, False))
            print()
    
    # 显示测试结果汇总
    print("=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print()
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统可以正常运行。")
        return True
    else:
        print("⚠️  部分测试失败，请检查配置和网络连接。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
