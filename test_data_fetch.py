#!/usr/bin/env python3
"""
测试数据获取功能
验证能否正确获取历史数据
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.api.binance_client import BinanceClient
from src.data.cached_data_manager import CachedDataManager
from datetime import datetime, timed<PERSON><PERSON>

def test_data_fetch():
    """测试数据获取"""
    print("测试数据获取功能...")
    
    try:
        client = BinanceClient()
        cache_manager = CachedDataManager(client)
        
        # 测试短期数据获取
        print("\n1. 测试短期数据获取 (7天)")
        try:
            data = cache_manager.get_historical_data('BTCUSDT', '1h', '2024-12-01', '2024-12-07')
            print(f"   ✓ 获取数据: {len(data)} 条记录")
            if not data.empty:
                print(f"   数据范围: {data.index[0]} 到 {data.index[-1]}")
        except Exception as e:
            print(f"   ✗ 失败: {e}")
        
        # 测试中期数据获取
        print("\n2. 测试中期数据获取 (30天)")
        try:
            data = cache_manager.get_historical_data('BTCUSDT', '1h', '2024-11-01', '2024-11-30')
            print(f"   ✓ 获取数据: {len(data)} 条记录")
            if not data.empty:
                print(f"   数据范围: {data.index[0]} 到 {data.index[-1]}")
        except Exception as e:
            print(f"   ✗ 失败: {e}")
        
        # 测试API限制
        print("\n3. 测试API数据限制")
        try:
            from src.data.data_fetcher import DataFetcher
            data_fetcher = DataFetcher(client)
            
            # 测试不同天数的数据获取
            for days in [7, 30, 90, 180]:
                try:
                    data = data_fetcher.get_historical_data('BTCUSDT', '1h', days=days)
                    print(f"   {days}天: {len(data)} 条记录")
                except Exception as e:
                    print(f"   {days}天: 失败 - {e}")
        except Exception as e:
            print(f"   API测试失败: {e}")
        
        # 测试缓存信息
        print("\n4. 缓存信息")
        cache_info = cache_manager.get_cache_info()
        print(f"   缓存文件: {cache_info['total_files']} 个")
        print(f"   缓存大小: {cache_info['total_size_mb']:.2f} MB")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_current_data():
    """测试当前可用数据"""
    print("\n测试当前可用数据...")
    
    try:
        client = BinanceClient()
        
        # 测试实时价格
        symbols = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT']
        for symbol in symbols:
            try:
                price = client.get_ticker_price(symbol)
                print(f"   {symbol}: ${price:,.2f}")
            except Exception as e:
                print(f"   {symbol}: 失败 - {e}")
        
        # 测试最近数据
        print("\n测试最近历史数据:")
        from src.data.data_fetcher import DataFetcher
        data_fetcher = DataFetcher(client)
        
        for symbol in symbols:
            try:
                data = data_fetcher.get_historical_data(symbol, '1h', days=1)
                print(f"   {symbol} 1小时: {len(data)} 条记录")
                if not data.empty:
                    print(f"     最新: {data.index[-1]} - ${data['close'].iloc[-1]:,.2f}")
            except Exception as e:
                print(f"   {symbol}: 失败 - {e}")
        
        return True
        
    except Exception as e:
        print(f"当前数据测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 数据获取测试")
    print("=" * 50)
    
    # 测试基本数据获取
    success1 = test_current_data()
    
    # 测试缓存数据获取
    success2 = test_data_fetch()
    
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    print(f"当前数据测试: {'✓ 通过' if success1 else '✗ 失败'}")
    print(f"缓存数据测试: {'✓ 通过' if success2 else '✗ 失败'}")
    
    if success1 and success2:
        print("\n🎉 所有测试通过！数据获取功能正常")
    else:
        print("\n⚠️ 部分测试失败，请检查网络连接和API配置")

if __name__ == "__main__":
    main()
