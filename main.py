#!/usr/bin/env python3
"""
币安量化交易系统主程序
整合所有模块，实现多策略并行运行
"""

import time
import signal
import sys
import threading
from typing import Dict, List
from datetime import datetime

# 导入配置和日志
from config.config import config
from src.utils.logger import get_logger, trading_logger

# 导入核心模块
from src.api.binance_client import BinanceClient
from src.data.data_fetcher import DataFetcher
from src.risk.risk_manager import RiskManager
from src.utils.monitor import PerformanceMonitor

# 导入策略
from src.strategies.doji_plus import DojiPlusStrategy
from src.strategies.triangle_arbitrage import TriangleArbitrageStrategy
from src.strategies.matrix_grid import MatrixGridStrategy

logger = get_logger(__name__)


class QuantTradingSystem:
    """量化交易系统主类"""
    
    def __init__(self):
        """初始化交易系统"""
        logger.info("=" * 60)
        logger.info("币安量化交易系统启动中...")
        logger.info("=" * 60)
        
        # 验证配置
        if not config.validate_config():
            logger.error("配置验证失败，请检查配置文件")
            sys.exit(1)
        
        # 初始化核心组件
        self.client = None
        self.data_fetcher = None
        self.risk_manager = None
        self.monitor = None
        
        # 策略管理
        self.strategies = {}
        self.strategy_threads = {}
        
        # 系统状态
        self.is_running = False
        self.shutdown_event = threading.Event()
        
        # 初始化组件
        self._initialize_components()
        
        # 注册信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        logger.info("量化交易系统初始化完成")
    
    def _initialize_components(self):
        """初始化系统组件"""
        try:
            # 初始化币安客户端
            logger.info("初始化币安API客户端...")
            self.client = BinanceClient()
            
            # 测试API连接
            if not self.client.test_connection():
                raise Exception("币安API连接失败")
            
            # 初始化数据获取器
            logger.info("初始化数据获取器...")
            self.data_fetcher = DataFetcher(self.client)
            
            # 初始化风险管理器
            logger.info("初始化风险管理器...")
            self.risk_manager = RiskManager(self.client)
            
            # 初始化性能监控器
            logger.info("初始化性能监控器...")
            self.monitor = PerformanceMonitor(self.client, self.risk_manager)
            
            # 初始化策略
            self._initialize_strategies()
            
        except Exception as e:
            logger.error(f"初始化组件失败: {e}")
            raise
    
    def _initialize_strategies(self):
        """初始化交易策略"""
        logger.info("初始化交易策略...")
        
        enabled_strategies = config.get_enabled_strategies()
        
        for strategy_name in enabled_strategies:
            try:
                strategy_config = config.get_strategy_config(strategy_name)
                
                if strategy_name == 'doji_plus':
                    strategy = DojiPlusStrategy(self.client, self.data_fetcher, strategy_config)
                elif strategy_name == 'triangle_arbitrage':
                    strategy = TriangleArbitrageStrategy(self.client, self.data_fetcher, strategy_config)
                elif strategy_name == 'grid_trading':
                    strategy = MatrixGridStrategy(self.client, self.data_fetcher, strategy_config)
                else:
                    logger.warning(f"未知策略: {strategy_name}")
                    continue
                
                self.strategies[strategy_name] = strategy
                logger.info(f"策略 {strategy_name} 初始化成功")
                
            except Exception as e:
                logger.error(f"初始化策略 {strategy_name} 失败: {e}")
    
    def start(self):
        """启动交易系统"""
        try:
            logger.info("启动量化交易系统...")
            self.is_running = True
            
            # 启动性能监控
            self.monitor.start_monitoring()
            
            # 启动所有策略
            for name, strategy in self.strategies.items():
                self._start_strategy(name, strategy)
            
            # 启动主循环
            self._main_loop()
            
        except Exception as e:
            logger.error(f"启动系统失败: {e}")
            self.shutdown()
    
    def _start_strategy(self, name: str, strategy):
        """启动单个策略"""
        try:
            strategy.start()
            
            # 创建策略运行线程
            thread = threading.Thread(
                target=self._strategy_loop,
                args=(name, strategy),
                daemon=True
            )
            thread.start()
            
            self.strategy_threads[name] = thread
            logger.info(f"策略 {name} 已启动")
            
        except Exception as e:
            logger.error(f"启动策略 {name} 失败: {e}")
    
    def _strategy_loop(self, name: str, strategy):
        """策略运行循环"""
        logger.info(f"策略 {name} 开始运行")
        
        while self.is_running and not self.shutdown_event.is_set():
            try:
                # 处理每个交易对
                for symbol in config.TRADING_PAIRS:
                    if self.shutdown_event.is_set():
                        break
                    
                    # 获取历史数据
                    data = self.data_fetcher.get_historical_data(symbol, '1h', days=30)
                    if len(data) < 50:
                        continue
                    
                    # 生成交易信号
                    signals = strategy.generate_signals(symbol, data)
                    
                    # 处理信号
                    for signal in signals:
                        if self.shutdown_event.is_set():
                            break
                        
                        # 风险检查
                        risk_ok, risk_reason = self.risk_manager.check_signal_risk(
                            signal, strategy.positions
                        )
                        
                        if risk_ok:
                            # 执行信号
                            success = strategy.execute_signal(signal)
                            if success:
                                trading_logger.log_signal(
                                    name, signal.symbol, signal.signal_type.value,
                                    signal.price, signal.quantity, signal.confidence, signal.reason
                                )
                        else:
                            logger.warning(f"信号被风险管理拒绝: {risk_reason}")
                    
                    # 更新持仓盈亏
                    current_prices = self.data_fetcher.get_realtime_prices(config.TRADING_PAIRS)
                    strategy.update_positions(current_prices)
                
                # 更新策略指标到监控器
                performance = strategy.get_performance_summary()
                self.monitor.update_strategy_metrics(name, performance)
                
                # 策略循环间隔
                time.sleep(30)
                
            except Exception as e:
                logger.error(f"策略 {name} 运行出错: {e}")
                time.sleep(60)  # 出错时等待更长时间
        
        logger.info(f"策略 {name} 已停止")
    
    def _main_loop(self):
        """主循环"""
        logger.info("进入主循环...")
        
        last_risk_update = 0
        
        while self.is_running and not self.shutdown_event.is_set():
            try:
                current_time = time.time()
                
                # 定期更新风险指标
                if current_time - last_risk_update > 60:  # 每分钟更新一次
                    all_positions = {}
                    for strategy in self.strategies.values():
                        all_positions.update(strategy.positions)
                    
                    current_prices = self.data_fetcher.get_realtime_prices(config.TRADING_PAIRS)
                    self.risk_manager.update_risk_metrics(all_positions, current_prices)
                    
                    last_risk_update = current_time
                
                # 检查是否需要重置日指标
                now = datetime.now()
                if now.hour == 0 and now.minute == 0:  # 每天0点重置
                    self.risk_manager.reset_daily_metrics()
                
                # 主循环间隔
                time.sleep(10)
                
            except Exception as e:
                logger.error(f"主循环出错: {e}")
                time.sleep(30)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        logger.info(f"收到信号 {signum}，开始关闭系统...")
        self.shutdown()
    
    def shutdown(self):
        """关闭系统"""
        logger.info("正在关闭量化交易系统...")
        
        self.is_running = False
        self.shutdown_event.set()
        
        # 停止所有策略
        for name, strategy in self.strategies.items():
            try:
                strategy.stop()
                logger.info(f"策略 {name} 已停止")
            except Exception as e:
                logger.error(f"停止策略 {name} 失败: {e}")
        
        # 等待策略线程结束
        for name, thread in self.strategy_threads.items():
            try:
                thread.join(timeout=10)
                logger.info(f"策略线程 {name} 已结束")
            except Exception as e:
                logger.error(f"等待策略线程 {name} 结束失败: {e}")
        
        # 停止监控
        if self.monitor:
            self.monitor.stop_monitoring()
        
        # 生成最终报告
        self._generate_final_report()
        
        logger.info("量化交易系统已关闭")
    
    def _generate_final_report(self):
        """生成最终报告"""
        try:
            logger.info("生成最终交易报告...")
            
            # 策略表现汇总
            for name, strategy in self.strategies.items():
                performance = strategy.get_performance_summary()
                trading_logger.log_performance(name, performance)
                logger.info(f"策略 {name} 表现: {performance}")
            
            # 风险报告
            risk_report = self.risk_manager.get_risk_report()
            logger.info(f"风险报告: {risk_report}")
            
            # 监控报告
            if self.monitor:
                monitor_report = self.monitor.get_monitoring_report()
                logger.info(f"监控报告: 活跃告警 {monitor_report.get('alerts', {}).get('active_count', 0)} 个")
            
        except Exception as e:
            logger.error(f"生成最终报告失败: {e}")
    
    def get_system_status(self) -> Dict:
        """获取系统状态"""
        try:
            return {
                'is_running': self.is_running,
                'strategies': {name: strategy.get_performance_summary() 
                             for name, strategy in self.strategies.items()},
                'risk_report': self.risk_manager.get_risk_report() if self.risk_manager else {},
                'monitor_report': self.monitor.get_monitoring_report() if self.monitor else {}
            }
        except Exception as e:
            logger.error(f"获取系统状态失败: {e}")
            return {'error': str(e)}


def main():
    """主函数"""
    try:
        # 创建交易系统
        trading_system = QuantTradingSystem()
        
        # 启动系统
        trading_system.start()
        
    except KeyboardInterrupt:
        logger.info("用户中断程序")
    except Exception as e:
        logger.error(f"程序异常退出: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
