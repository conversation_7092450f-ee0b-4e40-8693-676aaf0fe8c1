"""
日志配置模块
统一配置和管理系统日志
"""

import os
import logging
import logging.handlers
from datetime import datetime
from config.config import config


def setup_logger(name: str = None, level: str = None) -> logging.Logger:
    """设置日志器"""
    
    # 创建日志目录
    log_dir = os.path.dirname(config.LOG_FILE_PATH)
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 获取日志器
    logger_name = name or 'quant_trading'
    logger = logging.getLogger(logger_name)
    
    # 设置日志级别
    log_level = level or config.LOG_LEVEL
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # 避免重复添加处理器
    if logger.handlers:
        return logger
    
    # 创建格式器
    formatter = logging.Formatter(config.LOG_FORMAT)
    
    # 文件处理器 - 轮转日志
    file_handler = logging.handlers.RotatingFileHandler(
        config.LOG_FILE_PATH,
        maxBytes=config.LOG_MAX_BYTES,
        backupCount=config.LOG_BACKUP_COUNT,
        encoding='utf-8'
    )
    file_handler.setFormatter(formatter)
    file_handler.setLevel(logging.DEBUG)
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    console_handler.setLevel(logging.INFO)
    
    # 添加处理器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger


def get_logger(name: str = None) -> logging.Logger:
    """获取日志器"""
    return setup_logger(name)


class TradingLogger:
    """交易专用日志器"""
    
    def __init__(self):
        self.logger = get_logger('trading')
        self.trade_log_file = os.path.join(os.path.dirname(config.LOG_FILE_PATH), 'trades.log')
        
        # 创建交易日志处理器
        self.trade_handler = logging.handlers.RotatingFileHandler(
            self.trade_log_file,
            maxBytes=config.LOG_MAX_BYTES,
            backupCount=config.LOG_BACKUP_COUNT,
            encoding='utf-8'
        )
        
        trade_formatter = logging.Formatter(
            '%(asctime)s - TRADE - %(message)s'
        )
        self.trade_handler.setFormatter(trade_formatter)
        
        # 创建交易专用日志器
        self.trade_logger = logging.getLogger('trades')
        self.trade_logger.setLevel(logging.INFO)
        self.trade_logger.addHandler(self.trade_handler)
        self.trade_logger.propagate = False
    
    def log_signal(self, strategy: str, symbol: str, signal_type: str, price: float, 
                   quantity: float, confidence: float, reason: str):
        """记录交易信号"""
        message = (f"SIGNAL | {strategy} | {symbol} | {signal_type} | "
                  f"Price: {price:.6f} | Qty: {quantity:.6f} | "
                  f"Confidence: {confidence:.2f} | Reason: {reason}")
        self.trade_logger.info(message)
    
    def log_order(self, strategy: str, symbol: str, side: str, order_type: str,
                  quantity: float, price: float, order_id: int, status: str):
        """记录订单"""
        message = (f"ORDER | {strategy} | {symbol} | {side} | {order_type} | "
                  f"Qty: {quantity:.6f} | Price: {price:.6f} | "
                  f"ID: {order_id} | Status: {status}")
        self.trade_logger.info(message)
    
    def log_position(self, strategy: str, symbol: str, action: str, side: str,
                     quantity: float, entry_price: float, pnl: float = None):
        """记录持仓变化"""
        pnl_str = f" | PnL: {pnl:.2f}" if pnl is not None else ""
        message = (f"POSITION | {strategy} | {symbol} | {action} | {side} | "
                  f"Qty: {quantity:.6f} | Entry: {entry_price:.6f}{pnl_str}")
        self.trade_logger.info(message)
    
    def log_risk_event(self, event_type: str, symbol: str, details: str):
        """记录风险事件"""
        message = f"RISK | {event_type} | {symbol} | {details}"
        self.trade_logger.warning(message)
    
    def log_performance(self, strategy: str, metrics: dict):
        """记录策略表现"""
        message = (f"PERFORMANCE | {strategy} | "
                  f"Total Trades: {metrics.get('total_trades', 0)} | "
                  f"Win Rate: {metrics.get('win_rate', 0):.2f}% | "
                  f"Total PnL: {metrics.get('total_pnl', 0):.2f} | "
                  f"Max Drawdown: {metrics.get('max_drawdown', 0):.2f}%")
        self.trade_logger.info(message)


# 创建全局交易日志器实例
trading_logger = TradingLogger()
