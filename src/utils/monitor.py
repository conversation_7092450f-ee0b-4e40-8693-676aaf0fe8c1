"""
监控模块
实时监控系统状态、策略表现和风险指标
"""

import time
import threading
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import json
from src.api.binance_client import BinanceClient
from src.risk.risk_manager import RiskManager
from src.utils.logger import get_logger
from config.config import config

logger = get_logger(__name__)


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, client: BinanceClient, risk_manager: RiskManager):
        self.client = client
        self.risk_manager = risk_manager
        
        # 监控配置
        self.monitor_interval = config.MONITOR_INTERVAL
        self.is_monitoring = False
        self.monitor_thread = None
        
        # 监控数据
        self.system_metrics = {}
        self.strategy_metrics = {}
        self.alerts = []
        
        # 性能历史
        self.performance_history = []
        self.max_history_length = 1000
        
        logger.info("性能监控器初始化完成")
    
    def start_monitoring(self):
        """启动监控"""
        if self.is_monitoring:
            logger.warning("监控已在运行中")
            return
        
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        logger.info("性能监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        
        logger.info("性能监控已停止")
    
    def _monitor_loop(self):
        """监控主循环"""
        while self.is_monitoring:
            try:
                # 收集系统指标
                self._collect_system_metrics()
                
                # 检查告警条件
                self._check_alerts()
                
                # 记录性能历史
                self._record_performance_history()
                
                # 等待下一次监控
                time.sleep(self.monitor_interval)
                
            except Exception as e:
                logger.error(f"监控循环出错: {e}")
                time.sleep(10)  # 出错时等待更长时间
    
    def _collect_system_metrics(self):
        """收集系统指标"""
        try:
            # 获取账户信息
            account_info = self.client.get_account_info()
            
            # 获取风险报告
            risk_report = self.risk_manager.get_risk_report()
            
            # 更新系统指标
            self.system_metrics = {
                'timestamp': time.time(),
                'account': {
                    'total_balance': account_info['total_balance_usdt'],
                    'can_trade': account_info['can_trade'],
                    'balances': len([b for b in account_info['balances'] if b['total'] > 0])
                },
                'risk': {
                    'emergency_stop': risk_report['emergency_stop'],
                    'current_drawdown': risk_report['risk_metrics']['current_drawdown'],
                    'daily_pnl': risk_report['risk_metrics']['daily_pnl'],
                    'total_exposure': risk_report['risk_metrics']['total_exposure']
                },
                'system': {
                    'uptime': time.time() - getattr(self, 'start_time', time.time()),
                    'monitoring': self.is_monitoring
                }
            }
            
        except Exception as e:
            logger.error(f"收集系统指标失败: {e}")
    
    def update_strategy_metrics(self, strategy_name: str, metrics: Dict[str, Any]):
        """更新策略指标"""
        self.strategy_metrics[strategy_name] = {
            'timestamp': time.time(),
            **metrics
        }
    
    def _check_alerts(self):
        """检查告警条件"""
        try:
            current_time = time.time()
            
            # 清理过期告警
            self.alerts = [alert for alert in self.alerts 
                          if current_time - alert['timestamp'] < 3600]  # 保留1小时内的告警
            
            # 检查风险告警
            if self.system_metrics.get('risk', {}).get('emergency_stop'):
                self._add_alert('CRITICAL', 'RISK', '紧急停止状态', 
                              self.risk_manager.stop_reason)
            
            # 检查回撤告警
            current_drawdown = self.system_metrics.get('risk', {}).get('current_drawdown', 0)
            if current_drawdown > 0.05:  # 5%回撤告警
                self._add_alert('WARNING', 'RISK', '回撤告警', 
                              f'当前回撤: {current_drawdown:.2%}')
            
            # 检查日损失告警
            daily_pnl = self.system_metrics.get('risk', {}).get('daily_pnl', 0)
            if daily_pnl < -100:  # 日损失超过100 USDT
                self._add_alert('WARNING', 'PERFORMANCE', '日损失告警', 
                              f'日损失: {daily_pnl:.2f} USDT')
            
            # 检查账户状态告警
            can_trade = self.system_metrics.get('account', {}).get('can_trade', True)
            if not can_trade:
                self._add_alert('CRITICAL', 'ACCOUNT', '账户无法交易', 
                              '请检查账户状态和API权限')
            
        except Exception as e:
            logger.error(f"检查告警失败: {e}")
    
    def _add_alert(self, level: str, category: str, title: str, message: str):
        """添加告警"""
        alert = {
            'timestamp': time.time(),
            'level': level,
            'category': category,
            'title': title,
            'message': message
        }
        
        # 避免重复告警
        for existing_alert in self.alerts:
            if (existing_alert['category'] == category and 
                existing_alert['title'] == title and
                time.time() - existing_alert['timestamp'] < 300):  # 5分钟内不重复
                return
        
        self.alerts.append(alert)
        
        # 记录告警日志
        log_level = logging.CRITICAL if level == 'CRITICAL' else logging.WARNING
        logger.log(log_level, f"告警: {title} - {message}")
    
    def _record_performance_history(self):
        """记录性能历史"""
        try:
            if not self.system_metrics:
                return
            
            history_record = {
                'timestamp': time.time(),
                'balance': self.system_metrics.get('account', {}).get('total_balance', 0),
                'daily_pnl': self.system_metrics.get('risk', {}).get('daily_pnl', 0),
                'drawdown': self.system_metrics.get('risk', {}).get('current_drawdown', 0),
                'exposure': self.system_metrics.get('risk', {}).get('total_exposure', 0),
                'strategies': len(self.strategy_metrics),
                'alerts': len([a for a in self.alerts if time.time() - a['timestamp'] < 300])
            }
            
            self.performance_history.append(history_record)
            
            # 限制历史记录长度
            if len(self.performance_history) > self.max_history_length:
                self.performance_history = self.performance_history[-self.max_history_length:]
                
        except Exception as e:
            logger.error(f"记录性能历史失败: {e}")
    
    def get_monitoring_report(self) -> Dict[str, Any]:
        """获取监控报告"""
        try:
            # 计算统计信息
            recent_history = self.performance_history[-100:] if self.performance_history else []
            
            avg_balance = sum(h['balance'] for h in recent_history) / len(recent_history) if recent_history else 0
            max_drawdown = max((h['drawdown'] for h in recent_history), default=0)
            
            # 活跃告警
            active_alerts = [alert for alert in self.alerts 
                           if time.time() - alert['timestamp'] < 300]
            
            return {
                'monitoring_status': {
                    'is_monitoring': self.is_monitoring,
                    'monitor_interval': self.monitor_interval,
                    'uptime': self.system_metrics.get('system', {}).get('uptime', 0)
                },
                'current_metrics': self.system_metrics,
                'strategy_metrics': self.strategy_metrics,
                'alerts': {
                    'active_count': len(active_alerts),
                    'total_count': len(self.alerts),
                    'recent_alerts': active_alerts[-10:]  # 最近10个告警
                },
                'performance_summary': {
                    'avg_balance': avg_balance,
                    'max_drawdown': max_drawdown,
                    'history_length': len(self.performance_history)
                }
            }
            
        except Exception as e:
            logger.error(f"生成监控报告失败: {e}")
            return {'error': str(e)}
    
    def get_performance_chart_data(self, hours: int = 24) -> Dict[str, List]:
        """获取性能图表数据"""
        try:
            cutoff_time = time.time() - (hours * 3600)
            recent_data = [h for h in self.performance_history if h['timestamp'] > cutoff_time]
            
            if not recent_data:
                return {'timestamps': [], 'balances': [], 'pnl': [], 'drawdown': []}
            
            return {
                'timestamps': [h['timestamp'] for h in recent_data],
                'balances': [h['balance'] for h in recent_data],
                'pnl': [h['daily_pnl'] for h in recent_data],
                'drawdown': [h['drawdown'] * 100 for h in recent_data]  # 转换为百分比
            }
            
        except Exception as e:
            logger.error(f"获取图表数据失败: {e}")
            return {'timestamps': [], 'balances': [], 'pnl': [], 'drawdown': []}
    
    def export_monitoring_data(self, filepath: str):
        """导出监控数据"""
        try:
            export_data = {
                'export_time': datetime.now().isoformat(),
                'system_metrics': self.system_metrics,
                'strategy_metrics': self.strategy_metrics,
                'alerts': self.alerts,
                'performance_history': self.performance_history[-500:]  # 导出最近500条记录
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"监控数据已导出到: {filepath}")
            
        except Exception as e:
            logger.error(f"导出监控数据失败: {e}")
    
    def clear_alerts(self):
        """清理所有告警"""
        self.alerts.clear()
        logger.info("所有告警已清理")
    
    def clear_performance_history(self):
        """清理性能历史"""
        self.performance_history.clear()
        logger.info("性能历史已清理")
