"""
币安API客户端封装
提供统一的API接口，支持现货和期货交易
"""

import time
import logging
from typing import Dict, List, Optional, Any, Tuple
from decimal import Decimal
import pandas as pd
from binance.client import Client
from binance.exceptions import BinanceAPIException, BinanceOrderException
from config.config import config

logger = logging.getLogger(__name__)


class BinanceClient:
    """币安API客户端封装类"""
    
    def __init__(self):
        """初始化币安客户端"""
        self.api_key = config.BINANCE_API_KEY
        self.secret_key = config.BINANCE_SECRET_KEY
        self.testnet = config.BINANCE_TESTNET
        
        if not self.api_key or not self.secret_key:
            raise ValueError("币安API密钥未配置，请检查.env文件")
        
        # 初始化客户端
        self.client = Client(
            api_key=self.api_key,
            api_secret=self.secret_key,
            testnet=self.testnet
        )
        
        # 缓存交易对信息
        self._exchange_info = None
        self._symbol_info = {}
        
        logger.info(f"币安客户端初始化完成 (测试网: {self.testnet})")
    
    def test_connection(self) -> bool:
        """测试API连接"""
        try:
            self.client.ping()
            account_info = self.client.get_account()
            logger.info("币安API连接测试成功")
            logger.info(f"账户状态: {account_info.get('accountType', 'Unknown')}")
            return True
        except Exception as e:
            logger.error(f"币安API连接测试失败: {e}")
            return False
    
    def get_account_info(self) -> Dict[str, Any]:
        """获取账户信息"""
        try:
            account = self.client.get_account()
            balances = []
            
            for balance in account['balances']:
                free = float(balance['free'])
                locked = float(balance['locked'])
                if free > 0 or locked > 0:
                    balances.append({
                        'asset': balance['asset'],
                        'free': free,
                        'locked': locked,
                        'total': free + locked
                    })
            
            return {
                'account_type': account.get('accountType', 'SPOT'),
                'can_trade': account.get('canTrade', False),
                'can_withdraw': account.get('canWithdraw', False),
                'can_deposit': account.get('canDeposit', False),
                'balances': balances,
                'total_balance_usdt': self._calculate_total_balance_usdt(balances)
            }
        except BinanceAPIException as e:
            logger.error(f"获取账户信息失败: {e}")
            raise
    
    def get_symbol_info(self, symbol: str) -> Dict[str, Any]:
        """获取交易对信息"""
        if symbol in self._symbol_info:
            return self._symbol_info[symbol]
        
        try:
            if not self._exchange_info:
                self._exchange_info = self.client.get_exchange_info()
            
            for s in self._exchange_info['symbols']:
                if s['symbol'] == symbol:
                    info = {
                        'symbol': s['symbol'],
                        'status': s['status'],
                        'base_asset': s['baseAsset'],
                        'quote_asset': s['quoteAsset'],
                        'price_precision': s['quotePrecision'],
                        'quantity_precision': s['baseAssetPrecision'],
                        'min_qty': 0,
                        'max_qty': 0,
                        'step_size': 0,
                        'min_notional': 0
                    }
                    
                    # 解析过滤器
                    for f in s['filters']:
                        if f['filterType'] == 'LOT_SIZE':
                            info['min_qty'] = float(f['minQty'])
                            info['max_qty'] = float(f['maxQty'])
                            info['step_size'] = float(f['stepSize'])
                        elif f['filterType'] == 'MIN_NOTIONAL':
                            info['min_notional'] = float(f['minNotional'])
                    
                    self._symbol_info[symbol] = info
                    return info
            
            raise ValueError(f"交易对 {symbol} 不存在")
            
        except BinanceAPIException as e:
            logger.error(f"获取交易对信息失败: {e}")
            raise
    
    def get_ticker_price(self, symbol: str) -> float:
        """获取交易对当前价格"""
        try:
            ticker = self.client.get_symbol_ticker(symbol=symbol)
            return float(ticker['price'])
        except BinanceAPIException as e:
            logger.error(f"获取价格失败 {symbol}: {e}")
            raise
    
    def get_orderbook(self, symbol: str, limit: int = 100) -> Dict[str, Any]:
        """获取订单簿"""
        try:
            orderbook = self.client.get_order_book(symbol=symbol, limit=limit)
            return {
                'symbol': symbol,
                'bids': [[float(price), float(qty)] for price, qty in orderbook['bids']],
                'asks': [[float(price), float(qty)] for price, qty in orderbook['asks']],
                'timestamp': orderbook['lastUpdateId']
            }
        except BinanceAPIException as e:
            logger.error(f"获取订单簿失败 {symbol}: {e}")
            raise
    
    def get_klines(self, symbol: str, interval: str, limit: int = 500, 
                   start_time: Optional[int] = None, end_time: Optional[int] = None) -> pd.DataFrame:
        """获取K线数据"""
        try:
            klines = self.client.get_klines(
                symbol=symbol,
                interval=interval,
                limit=limit,
                startTime=start_time,
                endTime=end_time
            )
            
            df = pd.DataFrame(klines, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_volume', 'trades', 'taker_buy_base',
                'taker_buy_quote', 'ignore'
            ])
            
            # 转换数据类型
            numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'quote_volume']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col])
            
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            
            return df[['open', 'high', 'low', 'close', 'volume']]
            
        except BinanceAPIException as e:
            logger.error(f"获取K线数据失败 {symbol}: {e}")
            raise

    def place_order(self, symbol: str, side: str, order_type: str,
                    quantity: float, price: Optional[float] = None,
                    time_in_force: str = 'GTC') -> Dict[str, Any]:
        """下单"""
        try:
            # 获取交易对信息进行参数验证
            symbol_info = self.get_symbol_info(symbol)

            # 调整数量精度
            quantity = self._adjust_quantity(quantity, symbol_info)

            order_params = {
                'symbol': symbol,
                'side': side,
                'type': order_type,
                'quantity': quantity,
                'timeInForce': time_in_force
            }

            if price is not None:
                # 调整价格精度
                price = self._adjust_price(price, symbol_info)
                order_params['price'] = price

            # 下单
            if order_type == 'MARKET':
                if side == 'BUY':
                    order = self.client.order_market_buy(**{k: v for k, v in order_params.items() if k != 'timeInForce'})
                else:
                    order = self.client.order_market_sell(**{k: v for k, v in order_params.items() if k != 'timeInForce'})
            else:
                order = self.client.create_order(**order_params)

            logger.info(f"订单创建成功: {symbol} {side} {quantity} @ {price}")
            return self._format_order_response(order)

        except BinanceOrderException as e:
            logger.error(f"下单失败: {e}")
            raise
        except BinanceAPIException as e:
            logger.error(f"API错误: {e}")
            raise

    def cancel_order(self, symbol: str, order_id: int) -> Dict[str, Any]:
        """取消订单"""
        try:
            result = self.client.cancel_order(symbol=symbol, orderId=order_id)
            logger.info(f"订单取消成功: {symbol} {order_id}")
            return self._format_order_response(result)
        except BinanceAPIException as e:
            logger.error(f"取消订单失败: {e}")
            raise

    def get_order_status(self, symbol: str, order_id: int) -> Dict[str, Any]:
        """查询订单状态"""
        try:
            order = self.client.get_order(symbol=symbol, orderId=order_id)
            return self._format_order_response(order)
        except BinanceAPIException as e:
            logger.error(f"查询订单状态失败: {e}")
            raise

    def get_open_orders(self, symbol: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取未完成订单"""
        try:
            orders = self.client.get_open_orders(symbol=symbol)
            return [self._format_order_response(order) for order in orders]
        except BinanceAPIException as e:
            logger.error(f"获取未完成订单失败: {e}")
            raise

    def get_order_history(self, symbol: str, limit: int = 500) -> List[Dict[str, Any]]:
        """获取历史订单"""
        try:
            orders = self.client.get_all_orders(symbol=symbol, limit=limit)
            return [self._format_order_response(order) for order in orders]
        except BinanceAPIException as e:
            logger.error(f"获取历史订单失败: {e}")
            raise

    def _calculate_total_balance_usdt(self, balances: List[Dict]) -> float:
        """计算总余额(USDT) - 使用白名单方式"""
        total = 0.0

        # 稳定币白名单 - 按1:1计算
        stable_coins = {
            'USDT': 1.0, 'USDC': 1.0, 'BUSD': 1.0, 'TUSD': 1.0,
            'FDUSD': 1.0, 'USDP': 1.0, 'DAI': 1.0, 'XUSD': 1.0, 'USD1': 1.0
        }

        # 主要加密货币白名单 - 有USDT交易对的资产
        crypto_whitelist = {
            # 主流币
            'BTC', 'ETH', 'BNB', 'SOL', 'ADA', 'DOT', 'LINK', 'UNI', 'AVAX', 'MATIC',
            # 其他主要币种
            'LTC', 'TRX', 'XRP', 'NEO', 'QTUM', 'GAS', 'LRC', 'ZRX', 'KNC', 'IOTA',
            'XVG', 'MTL', 'ETC', 'ZEC', 'DASH', 'REQ', 'POWR', 'ENJ', 'STORJ', 'KMD',
            'BAT', 'LSK', 'MANA', 'ADX', 'XLM', 'ICX', 'RLC', 'PIVX', 'STEEM', 'ZIL',
            'ONT', 'WAN', 'SYS', 'ZEN', 'THETA', 'IOTX', 'DATA', 'SC', 'DENT', 'ARDR',
            'HOT', 'VET', 'RVN', 'ONG', 'FET', 'IOST', 'CELR', 'ATOM', 'PHB', 'TFUEL',
            'ONE', 'ALGO', 'DOGE', 'DUSK', 'ANKR', 'WIN', 'COS', 'FUN', 'CVC', 'CHZ',
            'BAND', 'XTZ', 'HBAR', 'NKN', 'STX', 'KAVA', 'ARPA', 'BCH', 'FTT', 'EUR',
            'OGN', 'BNT', 'LTO', 'MBL', 'COTI', 'CTSI', 'HIVE', 'CHR', 'MDT', 'COMP',
            'SXP', 'SNX', 'VTHO', 'DGB', 'MKR', 'RUNE', 'DCR', 'FIO', 'AVA', 'YFI',
            'JST', 'CRV', 'SAND', 'NMR', 'LUNA', 'RSR', 'PAXG', 'TRB', 'WBTC', 'SUSHI',
            'KSM', 'EGLD', 'DIA', 'UMA', 'BEL', 'OXT', 'SUN', 'FLM', 'SCRT', 'CAKE',
            'UTK', 'XVS', 'ALPHA', 'AAVE', 'NEAR', 'FIL', 'INJ', 'AUDIO', 'CTK', 'AXS',
            'SLP', 'STRAX', 'ROSE', 'SKL', 'GLM', 'GRT', 'JUV', 'PSG', '1INCH', 'OG',
            'ATM', 'ASR', 'CELO', 'RIF', 'TRU', 'CKB', 'TWT', 'SFP', 'DODO', 'ACM',
            'AUCTION', 'PHA', 'FIS', 'OM', 'POND', 'DEGO', 'ALICE', 'PERP', 'SUPER',
            'CFX', 'TKO', 'PUNDIX', 'TLM', 'BAR', 'FORTH', 'BAKE', 'SHIB', 'ICP', 'AR',
            'MASK', 'LPT', 'ATA', 'GTC', 'MLN', 'DEXE', 'C98', 'QNT', 'FLOW', 'MINA',
            'RAY', 'FARM', 'QUICK', 'MBOX', 'GHST', 'WAXP', 'GNO', 'XEC', 'DYDX', 'IDEX',
            'GALA', 'ILV', 'YGG', 'DF', 'FIDA', 'AGLD', 'RAD', 'RARE', 'SSV', 'LAZIO',
            'CHESS', 'MOVR', 'CITY', 'ENS', 'QI', 'PORTO', 'JASMY', 'AMP', 'PYR', 'ALCX',
            'SANTOS', 'BICO', 'FLUX', 'FXS', 'VOXEL', 'HIGH', 'CVX', 'PEOPLE', 'SPELL',
            'JOE', 'ACH', 'IMX', 'GLMR', 'LOKA', 'API3', 'BTTC', 'ACA', 'XNO', 'WOO',
            'ALPINE', 'T', 'ASTR', 'GMT', 'KDA', 'APE', 'BSW', 'BIFI', 'NEXO', 'REI',
            'LDO', 'OP', 'LEVER', 'STG', 'LUNC', 'GMX', 'POLYX', 'APT', 'OSMO', 'HFT',
            'HOOK', 'MAGIC', 'HIFI', 'RPL', 'GNS', 'SYN', 'LQTY', 'USTC', 'PROM', 'QKC',
            'ID', 'ARB', 'RDNT', 'EDU', 'SUI', 'PEPE', 'FLOKI', 'WBETH', 'MAV', 'PENDLE',
            'ARKM', 'WLD', 'SEI', 'CYBER', 'ARK', 'IQ', 'NTRN', 'TIA', 'MEME', 'ORDI',
            'BEAMX', 'VIC', 'BLUR', 'VANRY', 'JTO', '1000SATS', 'BONK', 'ACE', 'NFP',
            'AI', 'XAI', 'MANTA', 'ALT', 'JUP', 'PYTH', 'RONIN', 'DYM', 'PIXEL', 'STRK',
            'PORTAL', 'AXL', 'WIF', 'METIS', 'AEVO', 'BOME', 'ETHFI', 'ENA', 'W', 'TNSR',
            'SAGA', 'TAO', 'OMNI', 'REZ', 'BB', 'NOT', 'IO', 'ZK', 'LISTA', 'ZRO', 'G',
            'BANANA', 'RENDER', 'TON', 'DOGS', 'SLF', 'POL', 'NEIRO', 'TURBO', '1MBABYDOGE',
            'CATI', 'HMSTR', 'EIGEN', 'BNSOL', 'SCR', 'LUMIA', 'KAIA', 'COW', 'CETUS',
            'PNUT', 'ACT', 'USUAL', 'THE', 'ACX', 'ORCA', 'MOVE', 'ME', 'VELODROME',
            'VANA', '1000CAT', 'PENGU', 'BIO', 'ONDO', 'BIGTIME', 'VIRTUAL'
        }

        # 只处理价值较大的资产，提高计算速度
        significant_assets = []
        for balance in balances:
            asset = balance['asset']
            amount = balance['total']

            if amount <= 0:
                continue

            # 稳定币直接计算
            if asset in stable_coins:
                total += amount
                logger.debug(f"稳定币 {asset}: {amount:.2f}")
                continue

            # 主要资产优先处理
            major_assets = ['BTC', 'ETH', 'BNB', 'SOL', 'ADA', 'DOT', 'LINK', 'UNI', 'AVAX']
            if asset in major_assets:
                significant_assets.insert(0, (asset, amount))  # 优先处理
            elif asset in crypto_whitelist:
                significant_assets.append((asset, amount))

        # 处理加密货币资产（限制数量以提高速度）
        processed_count = 0
        max_assets_to_process = 50  # 最多处理50个资产

        for asset, amount in significant_assets:
            if processed_count >= max_assets_to_process:
                logger.debug(f"已处理{max_assets_to_process}个资产，跳过剩余资产以提高速度")
                break

            try:
                symbol = f"{asset}USDT"
                price = self.get_ticker_price(symbol)
                asset_value = amount * price
                total += asset_value
                logger.debug(f"{asset}: {amount:.6f} * {price:.6f} = {asset_value:.2f} USDT")
                processed_count += 1
            except Exception as e:
                logger.debug(f"获取 {asset} 价格失败，跳过: {e}")
                continue

        return total

    def _adjust_quantity(self, quantity: float, symbol_info: Dict) -> float:
        """调整数量精度"""
        step_size = symbol_info['step_size']
        precision = len(str(step_size).split('.')[-1]) if '.' in str(step_size) else 0
        return round(quantity, precision)

    def _adjust_price(self, price: float, symbol_info: Dict) -> float:
        """调整价格精度"""
        precision = symbol_info['price_precision']
        return round(price, precision)

    def _format_order_response(self, order: Dict) -> Dict[str, Any]:
        """格式化订单响应"""
        return {
            'order_id': int(order['orderId']),
            'symbol': order['symbol'],
            'side': order['side'],
            'type': order['type'],
            'quantity': float(order['origQty']),
            'price': float(order['price']) if order['price'] != '0.00000000' else None,
            'executed_qty': float(order['executedQty']),
            'status': order['status'],
            'time_in_force': order['timeInForce'],
            'timestamp': int(order['time']),
            'update_time': int(order['updateTime'])
        }
