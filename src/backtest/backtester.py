"""
回测模块
使用历史数据测试交易策略的表现
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from src.data.data_fetcher import DataFetcher
from src.data.indicators import TechnicalIndicators
from src.strategies.base_strategy import BaseStrategy, TradingSignal, SignalType
from config.config import config

logger = logging.getLogger(__name__)


class BacktestResult:
    """回测结果"""
    def __init__(self):
        self.initial_balance = 0.0
        self.final_balance = 0.0
        self.total_return = 0.0
        self.total_return_pct = 0.0
        self.max_drawdown = 0.0
        self.sharpe_ratio = 0.0
        self.win_rate = 0.0
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        self.avg_win = 0.0
        self.avg_loss = 0.0
        self.profit_factor = 0.0
        self.trades = []
        self.equity_curve = []
        self.daily_returns = []


class BacktestTrade:
    """回测交易记录"""
    def __init__(self, symbol: str, entry_time: datetime, entry_price: float,
                 exit_time: datetime, exit_price: float, quantity: float,
                 side: str, pnl: float, commission: float):
        self.symbol = symbol
        self.entry_time = entry_time
        self.entry_price = entry_price
        self.exit_time = exit_time
        self.exit_price = exit_price
        self.quantity = quantity
        self.side = side
        self.pnl = pnl
        self.commission = commission
        self.net_pnl = pnl - commission
        self.return_pct = (exit_price - entry_price) / entry_price if side == 'LONG' else (entry_price - exit_price) / entry_price


class Backtester:
    """回测器"""
    
    def __init__(self, data_fetcher: DataFetcher):
        self.data_fetcher = data_fetcher
        self.config = config.BACKTEST_CONFIG
        
        # 回测参数
        self.initial_balance = self.config['initial_balance']
        self.commission_rate = self.config['commission']
        self.slippage_rate = self.config['slippage']
        
        logger.info("回测器初始化完成")
    
    def run_backtest(self, strategy: BaseStrategy, symbol: str, 
                    start_date: str, end_date: str, timeframe: str = '1h') -> BacktestResult:
        """运行回测"""
        logger.info(f"开始回测: {strategy.name} - {symbol} ({start_date} 到 {end_date})")
        
        try:
            # 获取历史数据
            data = self._get_backtest_data(symbol, start_date, end_date, timeframe)
            if data.empty:
                raise ValueError("无法获取历史数据")
            
            # 初始化回测状态
            result = BacktestResult()
            result.initial_balance = self.initial_balance
            
            current_balance = self.initial_balance
            current_position = None
            peak_balance = self.initial_balance
            
            # 逐条处理数据
            for i in range(len(data)):
                current_data = data.iloc[:i+1]
                if len(current_data) < 50:  # 需要足够的数据计算指标
                    continue
                
                current_time = current_data.index[-1]
                current_price = current_data['close'].iloc[-1]
                
                # 更新权益曲线
                if current_position:
                    unrealized_pnl = self._calculate_unrealized_pnl(current_position, current_price)
                    current_equity = current_balance + unrealized_pnl
                else:
                    current_equity = current_balance
                
                result.equity_curve.append({
                    'timestamp': current_time,
                    'equity': current_equity,
                    'balance': current_balance
                })
                
                # 更新最大回撤
                if current_equity > peak_balance:
                    peak_balance = current_equity
                
                drawdown = (peak_balance - current_equity) / peak_balance
                if drawdown > result.max_drawdown:
                    result.max_drawdown = drawdown
                
                # 生成交易信号
                signals = strategy.generate_signals(symbol, current_data)
                
                for signal in signals:
                    if signal.signal_type == SignalType.BUY and not current_position:
                        # 开多仓
                        current_position = self._open_position(signal, current_time, current_balance, result)
                        if current_position:
                            current_balance -= current_position['cost']
                    
                    elif signal.signal_type == SignalType.SELL and not current_position:
                        # 开空仓 (现货不支持，跳过)
                        continue
                    
                    elif signal.signal_type in [SignalType.CLOSE_LONG, SignalType.CLOSE_SHORT] and current_position:
                        # 平仓
                        trade_result = self._close_position(current_position, signal, current_time, result)
                        if trade_result:
                            current_balance += trade_result['proceeds']
                            current_position = None
                
                # 检查止损止盈
                if current_position:
                    if self._should_stop_loss(current_position, current_price) or \
                       self._should_take_profit(current_position, current_price):
                        # 强制平仓
                        close_signal = TradingSignal(
                            symbol=symbol,
                            signal_type=SignalType.CLOSE_LONG,
                            price=current_price,
                            quantity=current_position['quantity'],
                            confidence=1.0,
                            reason="止损/止盈"
                        )
                        trade_result = self._close_position(current_position, close_signal, current_time, result)
                        if trade_result:
                            current_balance += trade_result['proceeds']
                            current_position = None
            
            # 如果还有未平仓位，强制平仓
            if current_position:
                final_price = data['close'].iloc[-1]
                final_time = data.index[-1]
                close_signal = TradingSignal(
                    symbol=symbol,
                    signal_type=SignalType.CLOSE_LONG,
                    price=final_price,
                    quantity=current_position['quantity'],
                    confidence=1.0,
                    reason="回测结束强制平仓"
                )
                trade_result = self._close_position(current_position, close_signal, final_time, result)
                if trade_result:
                    current_balance += trade_result['proceeds']
            
            # 计算最终结果
            result.final_balance = current_balance
            result.total_return = current_balance - self.initial_balance
            result.total_return_pct = result.total_return / self.initial_balance
            
            # 计算交易统计
            self._calculate_trade_statistics(result)
            
            # 计算夏普比率
            self._calculate_sharpe_ratio(result)
            
            logger.info(f"回测完成: 总收益 {result.total_return:.2f} ({result.total_return_pct:.2%})")
            
            return result
            
        except Exception as e:
            logger.error(f"回测失败: {e}")
            raise
    
    def _get_backtest_data(self, symbol: str, start_date: str, end_date: str, timeframe: str) -> pd.DataFrame:
        """获取回测数据"""
        try:
            # 这里简化处理，实际应该根据日期范围获取数据
            data = self.data_fetcher.get_historical_data(symbol, timeframe, days=90)
            
            # 计算技术指标
            data_with_indicators = TechnicalIndicators.calculate_all_indicators(data)
            
            return data_with_indicators
            
        except Exception as e:
            logger.error(f"获取回测数据失败: {e}")
            return pd.DataFrame()
    
    def _open_position(self, signal: TradingSignal, timestamp: datetime, 
                      available_balance: float, result: BacktestResult) -> Optional[Dict]:
        """开仓"""
        try:
            # 考虑滑点
            actual_price = signal.price * (1 + self.slippage_rate)
            
            # 计算可用数量
            max_quantity = available_balance / actual_price
            quantity = min(signal.quantity, max_quantity)
            
            if quantity <= 0:
                return None
            
            # 计算成本
            cost = quantity * actual_price
            commission = cost * self.commission_rate
            total_cost = cost + commission
            
            if total_cost > available_balance:
                return None
            
            position = {
                'symbol': signal.symbol,
                'side': 'LONG',
                'quantity': quantity,
                'entry_price': actual_price,
                'entry_time': timestamp,
                'cost': total_cost,
                'commission': commission
            }
            
            logger.debug(f"开仓: {signal.symbol} {quantity:.6f} @ {actual_price:.2f}")
            
            return position
            
        except Exception as e:
            logger.error(f"开仓失败: {e}")
            return None
    
    def _close_position(self, position: Dict, signal: TradingSignal, 
                       timestamp: datetime, result: BacktestResult) -> Optional[Dict]:
        """平仓"""
        try:
            # 考虑滑点
            actual_price = signal.price * (1 - self.slippage_rate)
            
            # 计算收益
            proceeds = position['quantity'] * actual_price
            commission = proceeds * self.commission_rate
            net_proceeds = proceeds - commission
            
            # 计算盈亏
            pnl = net_proceeds - position['cost']
            
            # 创建交易记录
            trade = BacktestTrade(
                symbol=position['symbol'],
                entry_time=position['entry_time'],
                entry_price=position['entry_price'],
                exit_time=timestamp,
                exit_price=actual_price,
                quantity=position['quantity'],
                side=position['side'],
                pnl=pnl,
                commission=position['commission'] + commission
            )
            
            result.trades.append(trade)
            
            logger.debug(f"平仓: {position['symbol']} {position['quantity']:.6f} @ {actual_price:.2f}, PnL: {pnl:.2f}")
            
            return {
                'proceeds': net_proceeds,
                'pnl': pnl
            }
            
        except Exception as e:
            logger.error(f"平仓失败: {e}")
            return None
    
    def _calculate_unrealized_pnl(self, position: Dict, current_price: float) -> float:
        """计算未实现盈亏"""
        if position['side'] == 'LONG':
            return (current_price - position['entry_price']) * position['quantity']
        else:
            return (position['entry_price'] - current_price) * position['quantity']
    
    def _should_stop_loss(self, position: Dict, current_price: float) -> bool:
        """检查是否应该止损"""
        if position['side'] == 'LONG':
            return current_price <= position['entry_price'] * (1 - 0.02)  # 2%止损
        else:
            return current_price >= position['entry_price'] * (1 + 0.02)
    
    def _should_take_profit(self, position: Dict, current_price: float) -> bool:
        """检查是否应该止盈"""
        if position['side'] == 'LONG':
            return current_price >= position['entry_price'] * (1 + 0.04)  # 4%止盈
        else:
            return current_price <= position['entry_price'] * (1 - 0.04)
    
    def _calculate_trade_statistics(self, result: BacktestResult):
        """计算交易统计"""
        if not result.trades:
            return
        
        result.total_trades = len(result.trades)
        
        winning_trades = [t for t in result.trades if t.net_pnl > 0]
        losing_trades = [t for t in result.trades if t.net_pnl < 0]
        
        result.winning_trades = len(winning_trades)
        result.losing_trades = len(losing_trades)
        result.win_rate = result.winning_trades / result.total_trades if result.total_trades > 0 else 0
        
        if winning_trades:
            result.avg_win = sum(t.net_pnl for t in winning_trades) / len(winning_trades)
        
        if losing_trades:
            result.avg_loss = sum(t.net_pnl for t in losing_trades) / len(losing_trades)
        
        # 计算盈亏比
        total_wins = sum(t.net_pnl for t in winning_trades)
        total_losses = abs(sum(t.net_pnl for t in losing_trades))
        
        if total_losses > 0:
            result.profit_factor = total_wins / total_losses
    
    def _calculate_sharpe_ratio(self, result: BacktestResult):
        """计算夏普比率"""
        if len(result.equity_curve) < 2:
            return
        
        # 计算日收益率
        daily_returns = []
        for i in range(1, len(result.equity_curve)):
            prev_equity = result.equity_curve[i-1]['equity']
            curr_equity = result.equity_curve[i]['equity']
            daily_return = (curr_equity - prev_equity) / prev_equity
            daily_returns.append(daily_return)
        
        if not daily_returns:
            return
        
        result.daily_returns = daily_returns
        
        # 计算夏普比率
        avg_return = np.mean(daily_returns)
        std_return = np.std(daily_returns)
        
        if std_return > 0:
            # 假设无风险利率为年化2%
            risk_free_rate = 0.02 / 365  # 日无风险利率
            result.sharpe_ratio = (avg_return - risk_free_rate) / std_return * np.sqrt(365)
    
    def generate_report(self, result: BacktestResult) -> str:
        """生成回测报告"""
        report = f"""
回测报告
{'='*50}

基本信息:
- 初始资金: {result.initial_balance:,.2f} USDT
- 最终资金: {result.final_balance:,.2f} USDT
- 总收益: {result.total_return:,.2f} USDT ({result.total_return_pct:.2%})
- 最大回撤: {result.max_drawdown:.2%}
- 夏普比率: {result.sharpe_ratio:.2f}

交易统计:
- 总交易次数: {result.total_trades}
- 盈利交易: {result.winning_trades}
- 亏损交易: {result.losing_trades}
- 胜率: {result.win_rate:.2%}
- 平均盈利: {result.avg_win:.2f} USDT
- 平均亏损: {result.avg_loss:.2f} USDT
- 盈亏比: {result.profit_factor:.2f}
"""
        return report
    
    def plot_results(self, result: BacktestResult, save_path: Optional[str] = None):
        """绘制回测结果图表"""
        try:
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
            
            # 权益曲线
            timestamps = [eq['timestamp'] for eq in result.equity_curve]
            equity_values = [eq['equity'] for eq in result.equity_curve]
            
            ax1.plot(timestamps, equity_values, label='权益曲线', linewidth=2)
            ax1.axhline(y=result.initial_balance, color='r', linestyle='--', alpha=0.7, label='初始资金')
            ax1.set_title('权益曲线')
            ax1.set_ylabel('资金 (USDT)')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            
            # 日收益率分布
            if result.daily_returns:
                ax2.hist(result.daily_returns, bins=50, alpha=0.7, edgecolor='black')
                ax2.axvline(x=np.mean(result.daily_returns), color='r', linestyle='--', label=f'平均收益率: {np.mean(result.daily_returns):.4f}')
                ax2.set_title('日收益率分布')
                ax2.set_xlabel('日收益率')
                ax2.set_ylabel('频次')
                ax2.legend()
                ax2.grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                logger.info(f"回测图表已保存到: {save_path}")
            else:
                plt.show()
                
        except Exception as e:
            logger.error(f"绘制回测结果失败: {e}")
