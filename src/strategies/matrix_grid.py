"""
MatrixHFGT网格交易策略
在价格区间内设置买卖网格，通过高频交易获取价差利润
支持动态网格调整和智能仓位管理
"""

import time
import logging
from typing import Dict, List, Optional, Any, Tuple
import pandas as pd
import numpy as np
from src.strategies.base_strategy import BaseStrategy, TradingSignal, SignalType, Position
from src.api.binance_client import BinanceClient
from src.data.data_fetcher import DataFetcher

logger = logging.getLogger(__name__)


class GridOrder:
    """网格订单"""
    def __init__(self, price: float, quantity: float, side: str, order_id: Optional[int] = None):
        self.price = price
        self.quantity = quantity
        self.side = side  # 'BUY' or 'SELL'
        self.order_id = order_id
        self.status = 'PENDING'  # PENDING, FILLED, CANCELLED
        self.timestamp = time.time()
    
    def to_dict(self) -> Dict:
        return {
            'price': self.price,
            'quantity': self.quantity,
            'side': self.side,
            'order_id': self.order_id,
            'status': self.status,
            'timestamp': self.timestamp
        }


class MatrixGridStrategy(BaseStrategy):
    """网格交易策略"""
    
    def __init__(self, client: BinanceClient, data_fetcher: DataFetcher, config: Dict[str, Any]):
        """初始化网格交易策略"""
        super().__init__("MatrixGrid", client, data_fetcher, config)
        
        # 网格参数
        self.grid_count = config.get('grid_count', 10)
        self.price_range_pct = config.get('price_range_pct', 0.1)  # 价格范围百分比
        self.order_amount_pct = config.get('order_amount_pct', 0.1)  # 每单金额占比
        self.rebalance_threshold = config.get('rebalance_threshold', 0.05)  # 重新平衡阈值
        self.max_orders = config.get('max_orders', 20)
        
        # 网格状态
        self.grids: Dict[str, Dict[str, Any]] = {}  # 每个交易对的网格信息
        self.active_orders: Dict[str, List[GridOrder]] = {}  # 活跃订单
        self.grid_profits: Dict[str, float] = {}  # 网格利润
        
        # 动态调整参数
        self.volatility_adjustment = True
        self.trend_following = True
        
        logger.info(f"网格交易策略初始化完成，网格数量: {self.grid_count}")
    
    def generate_signals(self, symbol: str, data: pd.DataFrame) -> List[TradingSignal]:
        """生成交易信号"""
        if len(data) < 50:
            return []
        
        signals = []
        
        # 初始化或更新网格
        if symbol not in self.grids:
            self._initialize_grid(symbol, data)
        else:
            self._update_grid_if_needed(symbol, data)
        
        # 检查网格订单状态
        self._check_grid_orders(symbol)
        
        # 生成新的网格订单信号
        new_signals = self._generate_grid_signals(symbol, data)
        signals.extend(new_signals)
        
        return signals
    
    def should_enter_position(self, symbol: str, data: pd.DataFrame) -> Optional[TradingSignal]:
        """判断是否应该开仓"""
        # 网格策略不使用传统的开仓逻辑，而是持续维护网格订单
        signals = self.generate_signals(symbol, data)
        
        # 返回最高优先级的信号
        if signals:
            return max(signals, key=lambda s: s.confidence)
        
        return None
    
    def should_exit_position(self, position: Position, current_price: float, data: pd.DataFrame) -> bool:
        """判断是否应该平仓"""
        # 网格策略通常不主动平仓，除非价格超出网格范围
        symbol = position.symbol
        
        if symbol in self.grids:
            grid_info = self.grids[symbol]
            upper_bound = grid_info['upper_price']
            lower_bound = grid_info['lower_price']
            
            # 如果价格超出网格范围太多，考虑平仓
            if current_price > upper_bound * 1.2 or current_price < lower_bound * 0.8:
                logger.info(f"价格超出网格范围，考虑平仓: {symbol} {current_price}")
                return True
        
        return False
    
    def _initialize_grid(self, symbol: str, data: pd.DataFrame):
        """初始化网格"""
        try:
            current_price = data['close'].iloc[-1]
            
            # 计算价格范围
            volatility = self._calculate_volatility(data)
            adjusted_range = self.price_range_pct * (1 + volatility)  # 根据波动率调整范围
            
            upper_price = current_price * (1 + adjusted_range / 2)
            lower_price = current_price * (1 - adjusted_range / 2)
            
            # 计算网格间距
            grid_spacing = (upper_price - lower_price) / self.grid_count
            
            # 生成网格价格
            grid_prices = []
            for i in range(self.grid_count + 1):
                price = lower_price + i * grid_spacing
                grid_prices.append(price)
            
            # 存储网格信息
            self.grids[symbol] = {
                'center_price': current_price,
                'upper_price': upper_price,
                'lower_price': lower_price,
                'grid_spacing': grid_spacing,
                'grid_prices': grid_prices,
                'last_update': time.time(),
                'volatility': volatility
            }
            
            # 初始化活跃订单列表
            self.active_orders[symbol] = []
            self.grid_profits[symbol] = 0.0
            
            logger.info(f"网格初始化完成: {symbol} 范围: {lower_price:.4f} - {upper_price:.4f}")
            
        except Exception as e:
            logger.error(f"初始化网格失败 {symbol}: {e}")
    
    def _update_grid_if_needed(self, symbol: str, data: pd.DataFrame):
        """根据需要更新网格"""
        try:
            grid_info = self.grids[symbol]
            current_price = data['close'].iloc[-1]
            
            # 检查是否需要重新平衡
            center_price = grid_info['center_price']
            price_deviation = abs(current_price - center_price) / center_price
            
            if price_deviation > self.rebalance_threshold:
                logger.info(f"网格需要重新平衡: {symbol} 偏差: {price_deviation:.4f}")
                
                # 取消所有活跃订单
                self._cancel_all_orders(symbol)
                
                # 重新初始化网格
                self._initialize_grid(symbol, data)
                
        except Exception as e:
            logger.error(f"更新网格失败 {symbol}: {e}")
    
    def _generate_grid_signals(self, symbol: str, data: pd.DataFrame) -> List[TradingSignal]:
        """生成网格交易信号"""
        signals = []

        try:
            grid_info = self.grids[symbol]
            current_price = data['close'].iloc[-1]

            # 计算每单数量 - 回测时使用固定金额
            try:
                account_info = self.client.get_account_info()
                usdt_balance = 0
                for balance in account_info['balances']:
                    if balance['asset'] == 'USDT':
                        usdt_balance = float(balance['free'])
                        break
                order_amount = usdt_balance * self.order_amount_pct
            except Exception:
                # 回测环境或API调用失败时使用默认值
                order_amount = 1000 * self.order_amount_pct  # 假设1000 USDT资金
            
            # 在当前价格附近放置买卖单
            grid_prices = grid_info['grid_prices']
            
            # 找到最接近当前价格的网格线
            closest_grid_idx = min(range(len(grid_prices)), 
                                 key=lambda i: abs(grid_prices[i] - current_price))
            
            # 在当前价格下方放置买单
            for i in range(max(0, closest_grid_idx - 2), closest_grid_idx):
                buy_price = grid_prices[i]
                if not self._has_order_at_price(symbol, buy_price, 'BUY'):
                    quantity = order_amount / buy_price
                    
                    signal = TradingSignal(
                        symbol=symbol,
                        signal_type=SignalType.BUY,
                        price=buy_price,
                        quantity=quantity,
                        confidence=0.8,
                        reason=f"网格买单 @ {buy_price:.4f}"
                    )
                    signals.append(signal)
            
            # 在当前价格上方放置卖单
            for i in range(closest_grid_idx + 1, min(len(grid_prices), closest_grid_idx + 3)):
                sell_price = grid_prices[i]
                if not self._has_order_at_price(symbol, sell_price, 'SELL'):
                    # 检查是否有足够的基础资产
                    base_asset = symbol.replace('USDT', '')
                    base_balance = 0

                    try:
                        # 尝试获取账户信息
                        for balance in account_info['balances']:
                            if balance['asset'] == base_asset:
                                base_balance = float(balance['free'])
                                break
                    except Exception:
                        # 回测环境中假设有一定的基础资产
                        base_balance = order_amount / current_price * 2  # 假设有足够的基础资产

                    if base_balance > 0:
                        quantity = min(base_balance * 0.5, order_amount / sell_price)

                        signal = TradingSignal(
                            symbol=symbol,
                            signal_type=SignalType.SELL,
                            price=sell_price,
                            quantity=quantity,
                            confidence=0.8,
                            reason=f"网格卖单 @ {sell_price:.4f}"
                        )
                        signals.append(signal)
            
        except Exception as e:
            logger.error(f"生成网格信号失败 {symbol}: {e}")
        
        return signals
    
    def _check_grid_orders(self, symbol: str):
        """检查网格订单状态"""
        if symbol not in self.active_orders:
            return

        try:
            # 获取所有未完成订单 - 回测时跳过
            try:
                open_orders = self.client.get_open_orders(symbol)
                open_order_ids = {order['order_id'] for order in open_orders}
            except Exception:
                # 回测环境中模拟订单状态
                open_order_ids = set()
            
            # 更新订单状态
            for grid_order in self.active_orders[symbol]:
                if grid_order.order_id and grid_order.order_id not in open_order_ids:
                    # 订单已完成
                    grid_order.status = 'FILLED'
                    logger.info(f"网格订单完成: {symbol} {grid_order.side} @ {grid_order.price}")
                    
                    # 计算利润
                    if grid_order.side == 'SELL':
                        profit = grid_order.quantity * grid_order.price * 0.001  # 简化利润计算
                        self.grid_profits[symbol] += profit
            
            # 移除已完成的订单
            self.active_orders[symbol] = [
                order for order in self.active_orders[symbol] 
                if order.status != 'FILLED'
            ]
            
        except Exception as e:
            logger.error(f"检查网格订单状态失败 {symbol}: {e}")
    
    def _has_order_at_price(self, symbol: str, price: float, side: str) -> bool:
        """检查指定价格是否已有订单"""
        if symbol not in self.active_orders:
            return False
        
        for order in self.active_orders[symbol]:
            if (abs(order.price - price) < price * 0.001 and 
                order.side == side and 
                order.status == 'PENDING'):
                return True
        
        return False
    
    def _cancel_all_orders(self, symbol: str):
        """取消所有活跃订单"""
        if symbol not in self.active_orders:
            return
        
        try:
            for grid_order in self.active_orders[symbol]:
                if grid_order.order_id and grid_order.status == 'PENDING':
                    self.client.cancel_order(symbol, grid_order.order_id)
                    grid_order.status = 'CANCELLED'
            
            # 清空活跃订单列表
            self.active_orders[symbol] = []
            
            logger.info(f"已取消所有网格订单: {symbol}")
            
        except Exception as e:
            logger.error(f"取消网格订单失败 {symbol}: {e}")
    
    def _calculate_volatility(self, data: pd.DataFrame, period: int = 20) -> float:
        """计算价格波动率"""
        returns = data['close'].pct_change().dropna()
        volatility = returns.rolling(window=period).std().iloc[-1]
        return volatility if not np.isnan(volatility) else 0.02
    
    def execute_grid_signal(self, signal: TradingSignal) -> bool:
        """执行网格信号"""
        try:
            # 下限价单
            order = self.client.place_order(
                symbol=signal.symbol,
                side=signal.signal_type.value,
                order_type='LIMIT',
                quantity=signal.quantity,
                price=signal.price
            )
            
            if order['status'] in ['NEW', 'PARTIALLY_FILLED']:
                # 创建网格订单记录
                grid_order = GridOrder(
                    price=signal.price,
                    quantity=signal.quantity,
                    side=signal.signal_type.value,
                    order_id=order['order_id']
                )
                
                # 添加到活跃订单列表
                if signal.symbol not in self.active_orders:
                    self.active_orders[signal.symbol] = []
                
                self.active_orders[signal.symbol].append(grid_order)
                
                logger.info(f"网格订单创建成功: {signal.symbol} {signal.signal_type.value} @ {signal.price}")
                return True
            
        except Exception as e:
            logger.error(f"执行网格信号失败: {e}")
        
        return False
    
    def get_grid_status(self, symbol: str) -> Dict[str, Any]:
        """获取网格状态"""
        if symbol not in self.grids:
            return {'error': '网格未初始化'}
        
        grid_info = self.grids[symbol]
        active_orders = self.active_orders.get(symbol, [])
        
        return {
            'symbol': symbol,
            'strategy': self.name,
            'grid_info': {
                'center_price': grid_info['center_price'],
                'upper_price': grid_info['upper_price'],
                'lower_price': grid_info['lower_price'],
                'grid_count': self.grid_count,
                'grid_spacing': grid_info['grid_spacing']
            },
            'active_orders': len(active_orders),
            'total_profit': self.grid_profits.get(symbol, 0.0),
            'orders': [order.to_dict() for order in active_orders]
        }
    
    def get_all_grids_summary(self) -> Dict[str, Any]:
        """获取所有网格摘要"""
        total_profit = sum(self.grid_profits.values())
        total_active_orders = sum(len(orders) for orders in self.active_orders.values())
        
        return {
            'strategy': self.name,
            'active_grids': len(self.grids),
            'total_active_orders': total_active_orders,
            'total_profit': total_profit,
            'grids': {symbol: self.get_grid_status(symbol) for symbol in self.grids.keys()}
        }
