"""
Triangle Theme三角套利策略
利用三个货币对之间的价格差异进行套利交易
例如: BTC/USDT, ETH/USDT, BTC/ETH 之间的套利机会
"""

import time
import logging
from typing import Dict, List, Optional, Any, Tuple
import pandas as pd
import numpy as np
from src.strategies.base_strategy import BaseStrategy, TradingSignal, SignalType, Position
from src.api.binance_client import BinanceClient
from src.data.data_fetcher import DataFetcher

logger = logging.getLogger(__name__)


class TriangleArbitrageStrategy(BaseStrategy):
    """三角套利策略"""
    
    def __init__(self, client: BinanceClient, data_fetcher: DataFetcher, config: Dict[str, Any]):
        """初始化三角套利策略"""
        super().__init__("TriangleArbitrage", client, data_fetcher, config)

        # 策略特定参数
        self.min_profit_threshold = config.get('min_profit_threshold', 0.001)  # 最小利润阈值 0.1%
        self.max_slippage = config.get('max_slippage', 0.002)  # 最大滑点 0.2%
        self.check_interval = config.get('check_interval', 5)  # 检查间隔(秒)

        # 验证并设置有效的三角组合
        self.triangles = self._get_valid_triangles(config.get('triangles', [
            ['BTC', 'ETH', 'USDT'],
            ['BTC', 'SOL', 'USDT'],
            ['ETH', 'SOL', 'USDT']
        ]))

        # 交易对映射
        self.symbol_mapping = {}
        self._build_symbol_mapping()

        # 套利机会历史
        self.arbitrage_opportunities = []
        self.last_check_time = 0

        logger.info(f"三角套利策略初始化完成，监控 {len(self.triangles)} 个有效三角组合")

    def _get_valid_triangles(self, triangles: List[List[str]]) -> List[List[str]]:
        """验证并返回有效的三角组合"""
        valid_triangles = []

        for triangle in triangles:
            if len(triangle) != 3:
                continue

            base, quote1, quote2 = triangle

            # 检查所需的交易对是否存在
            required_symbols = [
                f"{base}{quote2}",    # BTC/USDT
                f"{quote1}{quote2}",  # ETH/USDT
                f"{base}{quote1}"     # BTC/ETH
            ]

            all_valid = True
            for symbol in required_symbols:
                try:
                    # 尝试获取交易对信息来验证是否存在
                    self.client.get_symbol_info(symbol)
                except Exception:
                    logger.warning(f"交易对 {symbol} 不可用，跳过三角组合 {triangle}")
                    all_valid = False
                    break

            if all_valid:
                valid_triangles.append(triangle)
                logger.info(f"验证三角组合: {triangle} ✓")

        return valid_triangles

    def _build_symbol_mapping(self):
        """构建交易对映射"""
        for triangle in self.triangles:
            base, quote1, quote2 = triangle
            
            # 构建所有可能的交易对
            pairs = [
                f"{base}{quote2}",    # BTC/USDT
                f"{quote1}{quote2}",  # ETH/USDT
                f"{base}{quote1}"     # BTC/ETH
            ]
            
            self.symbol_mapping[tuple(triangle)] = pairs
            logger.info(f"三角套利组合: {triangle} -> {pairs}")
    
    def generate_signals(self, symbol: str, data: pd.DataFrame) -> List[TradingSignal]:
        """生成交易信号"""
        # 三角套利不基于单个交易对的历史数据，而是基于实时价格
        current_time = time.time()
        
        # 控制检查频率
        if current_time - self.last_check_time < self.check_interval:
            return []
        
        self.last_check_time = current_time
        
        signals = []
        
        # 检查所有三角套利机会
        for triangle in self.triangles:
            opportunities = self._find_arbitrage_opportunities(triangle)
            for opp in opportunities:
                if opp['profit_rate'] >= self.min_profit_threshold:
                    signal = self._create_arbitrage_signal(opp)
                    if signal:
                        signals.append(signal)
        
        return signals
    
    def should_enter_position(self, symbol: str, data: pd.DataFrame) -> Optional[TradingSignal]:
        """判断是否应该开仓"""
        # 三角套利策略不使用传统的开仓逻辑
        signals = self.generate_signals(symbol, data)
        
        for signal in signals:
            if signal.confidence >= 0.8:  # 高置信度的套利机会
                return signal
        
        return None
    
    def should_exit_position(self, position: Position, current_price: float, data: pd.DataFrame) -> bool:
        """判断是否应该平仓"""
        # 三角套利通常是快速完成的，不需要长期持仓
        # 如果持仓超过一定时间，应该平仓
        hold_time = time.time() - position.timestamp
        
        if hold_time > 300:  # 5分钟
            logger.info(f"三角套利持仓超时，强制平仓: {position.symbol}")
            return True
        
        return False
    
    def _find_arbitrage_opportunities(self, triangle: List[str]) -> List[Dict[str, Any]]:
        """寻找三角套利机会"""
        base, quote1, quote2 = triangle
        pairs = self.symbol_mapping[tuple(triangle)]
        
        try:
            # 获取实时价格
            prices = {}
            orderbooks = {}
            
            for pair in pairs:
                try:
                    prices[pair] = self.data_fetcher.get_realtime_price(pair, use_cache=True)
                    orderbooks[pair] = self.data_fetcher.get_orderbook_data(pair, depth=5)
                except Exception as e:
                    logger.warning(f"获取价格失败 {pair}: {e}")
                    return []
            
            # 检查两个方向的套利机会
            opportunities = []
            
            # 方向1: USDT -> BTC -> ETH -> USDT
            opp1 = self._calculate_arbitrage_profit_direction1(triangle, prices, orderbooks)
            if opp1:
                opportunities.append(opp1)
            
            # 方向2: USDT -> ETH -> BTC -> USDT
            opp2 = self._calculate_arbitrage_profit_direction2(triangle, prices, orderbooks)
            if opp2:
                opportunities.append(opp2)
            
            return opportunities
            
        except Exception as e:
            logger.error(f"寻找套利机会失败 {triangle}: {e}")
            return []
    
    def _calculate_arbitrage_profit_direction1(self, triangle: List[str], prices: Dict[str, float], 
                                            orderbooks: Dict[str, Dict]) -> Optional[Dict[str, Any]]:
        """计算方向1的套利利润: USDT -> BASE -> QUOTE1 -> USDT"""
        base, quote1, quote2 = triangle
        
        try:
            # 交易对
            base_quote2 = f"{base}{quote2}"      # BTC/USDT
            quote1_quote2 = f"{quote1}{quote2}"  # ETH/USDT
            base_quote1 = f"{base}{quote1}"      # BTC/ETH
            
            # 获取最优价格 (考虑订单簿深度)
            base_quote2_ask = orderbooks[base_quote2]['asks'][0][0]  # 买入BTC的价格
            base_quote1_bid = orderbooks[base_quote1]['bids'][0][0]  # 卖出BTC换ETH的价格
            quote1_quote2_bid = orderbooks[quote1_quote2]['bids'][0][0]  # 卖出ETH的价格
            
            # 计算套利路径: 1 USDT -> BTC -> ETH -> USDT
            initial_amount = 1000  # 假设1000 USDT
            
            # 步骤1: USDT买入BTC
            btc_amount = initial_amount / base_quote2_ask
            
            # 步骤2: BTC卖出换ETH
            eth_amount = btc_amount * base_quote1_bid
            
            # 步骤3: ETH卖出换USDT
            final_usdt = eth_amount * quote1_quote2_bid
            
            # 计算利润
            profit = final_usdt - initial_amount
            profit_rate = profit / initial_amount
            
            # 考虑手续费 (假设每笔交易0.1%手续费)
            total_fees = initial_amount * 0.001 * 3  # 三笔交易
            net_profit = profit - total_fees
            net_profit_rate = net_profit / initial_amount
            
            if net_profit_rate > 0:
                return {
                    'triangle': triangle,
                    'direction': 1,
                    'path': f"{quote2} -> {base} -> {quote1} -> {quote2}",
                    'initial_amount': initial_amount,
                    'final_amount': final_usdt,
                    'profit': profit,
                    'profit_rate': profit_rate,
                    'net_profit': net_profit,
                    'net_profit_rate': net_profit_rate,
                    'steps': [
                        {'action': 'BUY', 'symbol': base_quote2, 'price': base_quote2_ask, 'amount': btc_amount},
                        {'action': 'SELL', 'symbol': base_quote1, 'price': base_quote1_bid, 'amount': eth_amount},
                        {'action': 'SELL', 'symbol': quote1_quote2, 'price': quote1_quote2_bid, 'amount': final_usdt}
                    ],
                    'timestamp': time.time()
                }
            
        except Exception as e:
            logger.error(f"计算方向1套利失败: {e}")
        
        return None
    
    def _calculate_arbitrage_profit_direction2(self, triangle: List[str], prices: Dict[str, float], 
                                            orderbooks: Dict[str, Dict]) -> Optional[Dict[str, Any]]:
        """计算方向2的套利利润: USDT -> QUOTE1 -> BASE -> USDT"""
        base, quote1, quote2 = triangle
        
        try:
            # 交易对
            base_quote2 = f"{base}{quote2}"      # BTC/USDT
            quote1_quote2 = f"{quote1}{quote2}"  # ETH/USDT
            base_quote1 = f"{base}{quote1}"      # BTC/ETH
            
            # 获取最优价格
            quote1_quote2_ask = orderbooks[quote1_quote2]['asks'][0][0]  # 买入ETH的价格
            base_quote1_ask = orderbooks[base_quote1]['asks'][0][0]      # 用ETH买入BTC的价格
            base_quote2_bid = orderbooks[base_quote2]['bids'][0][0]      # 卖出BTC的价格
            
            # 计算套利路径: 1 USDT -> ETH -> BTC -> USDT
            initial_amount = 1000  # 假设1000 USDT
            
            # 步骤1: USDT买入ETH
            eth_amount = initial_amount / quote1_quote2_ask
            
            # 步骤2: ETH买入BTC
            btc_amount = eth_amount / base_quote1_ask
            
            # 步骤3: BTC卖出换USDT
            final_usdt = btc_amount * base_quote2_bid
            
            # 计算利润
            profit = final_usdt - initial_amount
            profit_rate = profit / initial_amount
            
            # 考虑手续费
            total_fees = initial_amount * 0.001 * 3
            net_profit = profit - total_fees
            net_profit_rate = net_profit / initial_amount
            
            if net_profit_rate > 0:
                return {
                    'triangle': triangle,
                    'direction': 2,
                    'path': f"{quote2} -> {quote1} -> {base} -> {quote2}",
                    'initial_amount': initial_amount,
                    'final_amount': final_usdt,
                    'profit': profit,
                    'profit_rate': profit_rate,
                    'net_profit': net_profit,
                    'net_profit_rate': net_profit_rate,
                    'steps': [
                        {'action': 'BUY', 'symbol': quote1_quote2, 'price': quote1_quote2_ask, 'amount': eth_amount},
                        {'action': 'BUY', 'symbol': base_quote1, 'price': base_quote1_ask, 'amount': btc_amount},
                        {'action': 'SELL', 'symbol': base_quote2, 'price': base_quote2_bid, 'amount': final_usdt}
                    ],
                    'timestamp': time.time()
                }
            
        except Exception as e:
            logger.error(f"计算方向2套利失败: {e}")
        
        return None
    
    def _create_arbitrage_signal(self, opportunity: Dict[str, Any]) -> Optional[TradingSignal]:
        """创建套利信号"""
        try:
            # 选择第一步交易作为主信号
            first_step = opportunity['steps'][0]
            
            signal = TradingSignal(
                symbol=first_step['symbol'],
                signal_type=SignalType.BUY if first_step['action'] == 'BUY' else SignalType.SELL,
                price=first_step['price'],
                quantity=first_step['amount'],
                confidence=min(opportunity['net_profit_rate'] * 100, 1.0),  # 利润率作为置信度
                reason=f"三角套利: {opportunity['path']}, 预期利润: {opportunity['net_profit_rate']:.4f}"
            )
            
            # 记录套利机会
            self.arbitrage_opportunities.append(opportunity)
            
            logger.info(f"发现套利机会: {opportunity['path']} 利润率: {opportunity['net_profit_rate']:.4f}")
            
            return signal
            
        except Exception as e:
            logger.error(f"创建套利信号失败: {e}")
            return None
    
    def execute_arbitrage(self, opportunity: Dict[str, Any]) -> bool:
        """执行套利交易"""
        try:
            logger.info(f"开始执行套利: {opportunity['path']}")
            
            # 按顺序执行所有步骤
            for i, step in enumerate(opportunity['steps']):
                logger.info(f"执行步骤 {i+1}: {step['action']} {step['symbol']} @ {step['price']}")
                
                order = self.client.place_order(
                    symbol=step['symbol'],
                    side=step['action'],
                    order_type='MARKET',
                    quantity=step['amount']
                )
                
                if order['status'] not in ['FILLED', 'PARTIALLY_FILLED']:
                    logger.error(f"套利步骤 {i+1} 执行失败: {order}")
                    return False
                
                # 短暂延迟，避免过快交易
                time.sleep(0.1)
            
            logger.info(f"套利执行完成: 预期利润 {opportunity['net_profit']:.2f} USDT")
            return True
            
        except Exception as e:
            logger.error(f"执行套利失败: {e}")
            return False
    
    def get_arbitrage_summary(self) -> Dict[str, Any]:
        """获取套利摘要"""
        recent_opportunities = [
            opp for opp in self.arbitrage_opportunities 
            if time.time() - opp['timestamp'] < 3600  # 最近1小时
        ]
        
        if not recent_opportunities:
            return {
                'total_opportunities': 0,
                'avg_profit_rate': 0,
                'best_opportunity': None
            }
        
        total_profit = sum(opp['net_profit_rate'] for opp in recent_opportunities)
        avg_profit_rate = total_profit / len(recent_opportunities)
        
        best_opportunity = max(recent_opportunities, key=lambda x: x['net_profit_rate'])
        
        return {
            'total_opportunities': len(recent_opportunities),
            'avg_profit_rate': avg_profit_rate,
            'best_opportunity': {
                'path': best_opportunity['path'],
                'profit_rate': best_opportunity['net_profit_rate'],
                'timestamp': best_opportunity['timestamp']
            },
            'triangles_monitored': len(self.triangles)
        }
