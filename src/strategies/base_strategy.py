"""
策略基类
定义所有交易策略的通用接口和基础功能
"""

import time
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
import pandas as pd
from src.api.binance_client import BinanceClient
from src.data.data_fetcher import DataFetcher
from src.data.indicators import TechnicalIndicators

logger = logging.getLogger(__name__)


class SignalType(Enum):
    """信号类型枚举"""
    BUY = "BUY"
    SELL = "SELL"
    HOLD = "HOLD"
    CLOSE_LONG = "CLOSE_LONG"
    CLOSE_SHORT = "CLOSE_SHORT"


class Position:
    """持仓信息"""
    def __init__(self, symbol: str, side: str, quantity: float, entry_price: float, 
                 timestamp: float, stop_loss: Optional[float] = None, take_profit: Optional[float] = None):
        self.symbol = symbol
        self.side = side  # 'LONG' or 'SHORT'
        self.quantity = quantity
        self.entry_price = entry_price
        self.timestamp = timestamp
        self.stop_loss = stop_loss
        self.take_profit = take_profit
        self.unrealized_pnl = 0.0
        self.realized_pnl = 0.0
    
    def update_pnl(self, current_price: float):
        """更新未实现盈亏"""
        if self.side == 'LONG':
            self.unrealized_pnl = (current_price - self.entry_price) * self.quantity
        else:  # SHORT
            self.unrealized_pnl = (self.entry_price - current_price) * self.quantity
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            'symbol': self.symbol,
            'side': self.side,
            'quantity': self.quantity,
            'entry_price': self.entry_price,
            'timestamp': self.timestamp,
            'stop_loss': self.stop_loss,
            'take_profit': self.take_profit,
            'unrealized_pnl': self.unrealized_pnl,
            'realized_pnl': self.realized_pnl
        }


class TradingSignal:
    """交易信号"""
    def __init__(self, symbol: str, signal_type: SignalType, price: float, 
                 quantity: float, confidence: float = 1.0, reason: str = ""):
        self.symbol = symbol
        self.signal_type = signal_type
        self.price = price
        self.quantity = quantity
        self.confidence = confidence  # 信号置信度 0-1
        self.reason = reason
        self.timestamp = time.time()
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            'symbol': self.symbol,
            'signal_type': self.signal_type.value,
            'price': self.price,
            'quantity': self.quantity,
            'confidence': self.confidence,
            'reason': self.reason,
            'timestamp': self.timestamp
        }


class BaseStrategy(ABC):
    """策略基类"""
    
    def __init__(self, name: str, client: BinanceClient, data_fetcher: DataFetcher, config: Dict[str, Any]):
        """初始化策略"""
        self.name = name
        self.client = client
        self.data_fetcher = data_fetcher
        self.config = config
        self.indicators = TechnicalIndicators()
        
        # 策略状态
        self.is_active = False
        self.positions: Dict[str, Position] = {}
        self.signals_history: List[TradingSignal] = []
        self.performance_metrics = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'total_pnl': 0.0,
            'max_drawdown': 0.0,
            'sharpe_ratio': 0.0
        }
        
        # 风险管理参数
        self.max_position_size = config.get('max_position_size', 1000)
        self.stop_loss_pct = config.get('stop_loss_pct', 0.02)
        self.take_profit_pct = config.get('take_profit_pct', 0.04)
        
        logger.info(f"策略 {self.name} 初始化完成")
    
    @abstractmethod
    def generate_signals(self, symbol: str, data: pd.DataFrame) -> List[TradingSignal]:
        """生成交易信号 - 子类必须实现"""
        pass
    
    @abstractmethod
    def should_enter_position(self, symbol: str, data: pd.DataFrame) -> Optional[TradingSignal]:
        """判断是否应该开仓 - 子类必须实现"""
        pass
    
    @abstractmethod
    def should_exit_position(self, position: Position, current_price: float, data: pd.DataFrame) -> bool:
        """判断是否应该平仓 - 子类必须实现"""
        pass
    
    def start(self):
        """启动策略"""
        self.is_active = True
        logger.info(f"策略 {self.name} 已启动")
    
    def stop(self):
        """停止策略"""
        self.is_active = False
        logger.info(f"策略 {self.name} 已停止")
    
    def add_position(self, position: Position):
        """添加持仓"""
        self.positions[position.symbol] = position
        logger.info(f"添加持仓: {position.symbol} {position.side} {position.quantity}")
    
    def remove_position(self, symbol: str) -> Optional[Position]:
        """移除持仓"""
        if symbol in self.positions:
            position = self.positions.pop(symbol)
            logger.info(f"移除持仓: {symbol}")
            return position
        return None
    
    def get_position(self, symbol: str) -> Optional[Position]:
        """获取持仓"""
        return self.positions.get(symbol)
    
    def has_position(self, symbol: str) -> bool:
        """检查是否有持仓"""
        return symbol in self.positions
    
    def update_positions(self, prices: Dict[str, float]):
        """更新所有持仓的盈亏"""
        for symbol, position in self.positions.items():
            if symbol in prices:
                position.update_pnl(prices[symbol])
    
    def calculate_position_size(self, symbol: str, price: float, risk_amount: float) -> float:
        """计算仓位大小"""
        # 基于风险金额计算仓位
        stop_loss_distance = price * self.stop_loss_pct
        position_size = risk_amount / stop_loss_distance
        
        # 限制最大仓位
        max_size = self.max_position_size / price
        position_size = min(position_size, max_size)
        
        return position_size
    
    def check_risk_management(self, signal: TradingSignal) -> bool:
        """风险管理检查"""
        # 检查最大持仓数量
        if len(self.positions) >= self.config.get('max_positions', 5):
            logger.warning(f"达到最大持仓数量限制: {len(self.positions)}")
            return False
        
        # 检查单个仓位大小
        position_value = signal.price * signal.quantity
        if position_value > self.max_position_size:
            logger.warning(f"仓位大小超限: {position_value} > {self.max_position_size}")
            return False
        
        return True
    
    def execute_signal(self, signal: TradingSignal) -> bool:
        """执行交易信号"""
        if not self.is_active:
            return False
        
        if not self.check_risk_management(signal):
            return False
        
        try:
            if signal.signal_type == SignalType.BUY:
                return self._execute_buy_signal(signal)
            elif signal.signal_type == SignalType.SELL:
                return self._execute_sell_signal(signal)
            elif signal.signal_type in [SignalType.CLOSE_LONG, SignalType.CLOSE_SHORT]:
                return self._execute_close_signal(signal)
            
        except Exception as e:
            logger.error(f"执行信号失败: {e}")
            return False
        
        return False
    
    def _execute_buy_signal(self, signal: TradingSignal) -> bool:
        """执行买入信号"""
        try:
            # 计算止损止盈价格
            stop_loss = signal.price * (1 - self.stop_loss_pct)
            take_profit = signal.price * (1 + self.take_profit_pct)
            
            # 下市价单
            order = self.client.place_order(
                symbol=signal.symbol,
                side='BUY',
                order_type='MARKET',
                quantity=signal.quantity
            )
            
            if order['status'] in ['FILLED', 'PARTIALLY_FILLED']:
                # 创建持仓记录
                position = Position(
                    symbol=signal.symbol,
                    side='LONG',
                    quantity=signal.quantity,
                    entry_price=signal.price,
                    timestamp=time.time(),
                    stop_loss=stop_loss,
                    take_profit=take_profit
                )
                self.add_position(position)
                
                # 记录信号
                self.signals_history.append(signal)
                self.performance_metrics['total_trades'] += 1
                
                logger.info(f"买入执行成功: {signal.symbol} {signal.quantity} @ {signal.price}")
                return True
            
        except Exception as e:
            logger.error(f"买入执行失败: {e}")
        
        return False
    
    def _execute_sell_signal(self, signal: TradingSignal) -> bool:
        """执行卖出信号"""
        # 现货交易暂不支持做空，这里可以扩展为期货交易
        logger.warning("现货交易不支持做空操作")
        return False
    
    def _execute_close_signal(self, signal: TradingSignal) -> bool:
        """执行平仓信号"""
        position = self.get_position(signal.symbol)
        if not position:
            logger.warning(f"没有找到持仓: {signal.symbol}")
            return False
        
        try:
            # 下市价单平仓
            order = self.client.place_order(
                symbol=signal.symbol,
                side='SELL',  # 平多仓
                order_type='MARKET',
                quantity=position.quantity
            )
            
            if order['status'] in ['FILLED', 'PARTIALLY_FILLED']:
                # 计算实现盈亏
                if position.side == 'LONG':
                    pnl = (signal.price - position.entry_price) * position.quantity
                else:
                    pnl = (position.entry_price - signal.price) * position.quantity
                
                position.realized_pnl = pnl
                self.performance_metrics['total_pnl'] += pnl
                
                if pnl > 0:
                    self.performance_metrics['winning_trades'] += 1
                else:
                    self.performance_metrics['losing_trades'] += 1
                
                # 移除持仓
                self.remove_position(signal.symbol)
                
                # 记录信号
                self.signals_history.append(signal)
                
                logger.info(f"平仓执行成功: {signal.symbol} PnL: {pnl:.2f}")
                return True
                
        except Exception as e:
            logger.error(f"平仓执行失败: {e}")
        
        return False
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取策略表现摘要"""
        total_trades = self.performance_metrics['total_trades']
        winning_trades = self.performance_metrics['winning_trades']
        
        win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
        
        return {
            'strategy_name': self.name,
            'is_active': self.is_active,
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': self.performance_metrics['losing_trades'],
            'win_rate': win_rate,
            'total_pnl': self.performance_metrics['total_pnl'],
            'active_positions': len(self.positions),
            'max_drawdown': self.performance_metrics['max_drawdown']
        }
