#!/usr/bin/env python3
"""
缓存数据管理器
负责管理历史数据的本地缓存，避免重复API调用
"""

import os
import pickle
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, Optional, List
import logging
from pathlib import Path

from src.api.binance_client import BinanceClient
from src.data.data_fetcher import DataFetcher

logger = logging.getLogger(__name__)


class CachedDataManager:
    """缓存数据管理器"""
    
    def __init__(self, client: BinanceClient, cache_dir: str = "data_cache"):
        """初始化缓存数据管理器"""
        self.client = client
        self.data_fetcher = DataFetcher(client)
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        
        logger.info(f"缓存数据管理器初始化完成，缓存目录: {self.cache_dir}")
    
    def _get_cache_filename(self, symbol: str, timeframe: str, start_date: str, end_date: str) -> str:
        """生成缓存文件名"""
        return f"{symbol}_{timeframe}_{start_date}_{end_date}.pkl"
    
    def _load_cached_data(self, cache_file: Path) -> Optional[pd.DataFrame]:
        """加载缓存数据"""
        try:
            if cache_file.exists():
                with open(cache_file, 'rb') as f:
                    data = pickle.load(f)
                logger.info(f"从缓存加载数据: {cache_file.name}")
                return data
        except Exception as e:
            logger.warning(f"加载缓存数据失败 {cache_file}: {e}")
        return None
    
    def _save_cached_data(self, data: pd.DataFrame, cache_file: Path):
        """保存数据到缓存"""
        try:
            with open(cache_file, 'wb') as f:
                pickle.dump(data, f)
            logger.info(f"数据已缓存: {cache_file.name}")
        except Exception as e:
            logger.error(f"保存缓存数据失败 {cache_file}: {e}")
    
    def get_historical_data(self, symbol: str, timeframe: str, start_date: str, end_date: str, 
                          force_refresh: bool = False) -> pd.DataFrame:
        """
        获取历史数据，优先使用缓存
        
        Args:
            symbol: 交易对
            timeframe: 时间框架
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            force_refresh: 强制刷新缓存
        """
        cache_filename = self._get_cache_filename(symbol, timeframe, start_date, end_date)
        cache_file = self.cache_dir / cache_filename
        
        # 检查缓存
        if not force_refresh:
            cached_data = self._load_cached_data(cache_file)
            if cached_data is not None:
                return cached_data
        
        # 从API获取数据
        logger.info(f"从API获取历史数据: {symbol} {timeframe} {start_date} 到 {end_date}")
        try:
            # 计算天数
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')
            days = (end_dt - start_dt).days + 1
            
            # 获取数据
            data = self.data_fetcher.get_historical_data(symbol, timeframe, days=days)
            
            # 过滤日期范围
            data = data[start_date:end_date]
            
            # 保存到缓存
            self._save_cached_data(data, cache_file)
            
            logger.info(f"获取历史数据完成: {len(data)} 条记录")
            return data
            
        except Exception as e:
            logger.error(f"获取历史数据失败 {symbol}: {e}")
            raise
    
    def preload_backtest_data(self, symbols: List[str], timeframes: List[str], 
                            start_date: str, end_date: str) -> Dict[str, Dict[str, pd.DataFrame]]:
        """
        预加载回测所需的所有数据
        
        Returns:
            {symbol: {timeframe: data}}
        """
        logger.info("开始预加载回测数据...")
        
        all_data = {}
        total_combinations = len(symbols) * len(timeframes)
        current = 0
        
        for symbol in symbols:
            all_data[symbol] = {}
            for timeframe in timeframes:
                current += 1
                logger.info(f"预加载进度: {current}/{total_combinations} - {symbol} {timeframe}")
                
                try:
                    data = self.get_historical_data(symbol, timeframe, start_date, end_date)
                    all_data[symbol][timeframe] = data
                    logger.info(f"✓ {symbol} {timeframe}: {len(data)} 条记录")
                except Exception as e:
                    logger.error(f"✗ {symbol} {timeframe}: {e}")
                    all_data[symbol][timeframe] = pd.DataFrame()
        
        logger.info("回测数据预加载完成")
        return all_data
    
    def get_cache_info(self) -> Dict[str, any]:
        """获取缓存信息"""
        cache_files = list(self.cache_dir.glob("*.pkl"))
        total_size = sum(f.stat().st_size for f in cache_files)
        
        return {
            'cache_dir': str(self.cache_dir),
            'total_files': len(cache_files),
            'total_size_mb': total_size / (1024 * 1024),
            'files': [f.name for f in cache_files]
        }
    
    def clear_cache(self, symbol: str = None, timeframe: str = None):
        """清理缓存"""
        if symbol and timeframe:
            # 清理特定缓存
            pattern = f"{symbol}_{timeframe}_*.pkl"
            files = list(self.cache_dir.glob(pattern))
        elif symbol:
            # 清理特定交易对的所有缓存
            pattern = f"{symbol}_*.pkl"
            files = list(self.cache_dir.glob(pattern))
        else:
            # 清理所有缓存
            files = list(self.cache_dir.glob("*.pkl"))
        
        for file in files:
            try:
                file.unlink()
                logger.info(f"删除缓存文件: {file.name}")
            except Exception as e:
                logger.error(f"删除缓存文件失败 {file}: {e}")
        
        logger.info(f"缓存清理完成，删除了 {len(files)} 个文件")


def test_cached_data_manager():
    """测试缓存数据管理器"""
    from src.api.binance_client import BinanceClient
    
    print("测试缓存数据管理器...")
    
    client = BinanceClient()
    cache_manager = CachedDataManager(client)
    
    # 测试获取数据
    data = cache_manager.get_historical_data('BTCUSDT', '1h', '2024-07-01', '2024-07-07')
    print(f"获取数据: {len(data)} 条记录")
    
    # 测试缓存信息
    cache_info = cache_manager.get_cache_info()
    print(f"缓存信息: {cache_info}")
    
    print("测试完成")


if __name__ == "__main__":
    test_cached_data_manager()
