"""
技术指标计算模块
使用TA-Lib库计算各种技术指标
"""

import numpy as np
import pandas as pd
import talib
from typing import Dict, List, Tuple, Optional
import logging

logger = logging.getLogger(__name__)


class TechnicalIndicators:
    """技术指标计算器"""
    
    @staticmethod
    def sma(data: pd.Series, period: int) -> pd.Series:
        """简单移动平均线"""
        return talib.SMA(data.values, timeperiod=period)
    
    @staticmethod
    def ema(data: pd.Series, period: int) -> pd.Series:
        """指数移动平均线"""
        return talib.EMA(data.values, timeperiod=period)
    
    @staticmethod
    def rsi(data: pd.Series, period: int = 14) -> pd.Series:
        """相对强弱指数"""
        return talib.RSI(data.values, timeperiod=period)
    
    @staticmethod
    def macd(data: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> <PERSON><PERSON>[pd.Series, pd.Series, pd.Series]:
        """MACD指标"""
        macd_line, signal_line, histogram = talib.MACD(data.values, fastperiod=fast, slowperiod=slow, signalperiod=signal)
        return macd_line, signal_line, histogram
    
    @staticmethod
    def bollinger_bands(data: pd.Series, period: int = 20, std_dev: int = 2) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """布林带"""
        upper, middle, lower = talib.BBANDS(data.values, timeperiod=period, nbdevup=std_dev, nbdevdn=std_dev)
        return upper, middle, lower
    
    @staticmethod
    def stochastic(high: pd.Series, low: pd.Series, close: pd.Series, 
                   k_period: int = 14, d_period: int = 3) -> Tuple[pd.Series, pd.Series]:
        """随机指标"""
        k_percent, d_percent = talib.STOCH(high.values, low.values, close.values, 
                                          fastk_period=k_period, slowk_period=d_period, slowd_period=d_period)
        return k_percent, d_percent
    
    @staticmethod
    def atr(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """平均真实范围"""
        return talib.ATR(high.values, low.values, close.values, timeperiod=period)
    
    @staticmethod
    def adx(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """平均方向指数"""
        return talib.ADX(high.values, low.values, close.values, timeperiod=period)
    
    @staticmethod
    def cci(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """商品通道指数"""
        return talib.CCI(high.values, low.values, close.values, timeperiod=period)
    
    @staticmethod
    def williams_r(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """威廉指标"""
        return talib.WILLR(high.values, low.values, close.values, timeperiod=period)
    
    @staticmethod
    def obv(close: pd.Series, volume: pd.Series) -> pd.Series:
        """能量潮指标"""
        return talib.OBV(close.values, volume.values)
    
    @staticmethod
    def doji_pattern(open_price: pd.Series, high: pd.Series, low: pd.Series, close: pd.Series) -> pd.Series:
        """十字星形态识别"""
        return talib.CDLDOJI(open_price.values, high.values, low.values, close.values)
    
    @staticmethod
    def hammer_pattern(open_price: pd.Series, high: pd.Series, low: pd.Series, close: pd.Series) -> pd.Series:
        """锤子形态识别"""
        return talib.CDLHAMMER(open_price.values, high.values, low.values, close.values)
    
    @staticmethod
    def engulfing_pattern(open_price: pd.Series, high: pd.Series, low: pd.Series, close: pd.Series) -> pd.Series:
        """吞没形态识别"""
        return talib.CDLENGULFING(open_price.values, high.values, low.values, close.values)
    
    @staticmethod
    def calculate_support_resistance(data: pd.DataFrame, window: int = 20) -> Dict[str, List[float]]:
        """计算支撑阻力位"""
        highs = data['high'].rolling(window=window).max()
        lows = data['low'].rolling(window=window).min()
        
        # 找到局部高点和低点
        resistance_levels = []
        support_levels = []
        
        for i in range(window, len(data) - window):
            # 检查是否为局部高点
            if data['high'].iloc[i] == highs.iloc[i]:
                resistance_levels.append(data['high'].iloc[i])
            
            # 检查是否为局部低点
            if data['low'].iloc[i] == lows.iloc[i]:
                support_levels.append(data['low'].iloc[i])
        
        # 去重并排序
        resistance_levels = sorted(list(set(resistance_levels)), reverse=True)
        support_levels = sorted(list(set(support_levels)))
        
        return {
            'resistance': resistance_levels[:5],  # 返回前5个阻力位
            'support': support_levels[:5]         # 返回前5个支撑位
        }
    
    @staticmethod
    def calculate_volatility(data: pd.Series, period: int = 20) -> pd.Series:
        """计算波动率"""
        returns = data.pct_change()
        volatility = returns.rolling(window=period).std() * np.sqrt(period)
        return volatility
    
    @staticmethod
    def calculate_momentum(data: pd.Series, period: int = 10) -> pd.Series:
        """计算动量指标"""
        return data.diff(period)
    
    @staticmethod
    def calculate_trend_strength(data: pd.DataFrame, period: int = 20) -> Dict[str, float]:
        """计算趋势强度"""
        close = data['close']
        
        # 计算移动平均线
        ma_short = TechnicalIndicators.sma(close, period // 2)
        ma_long = TechnicalIndicators.sma(close, period)
        
        # 计算ADX
        adx = TechnicalIndicators.adx(data['high'], data['low'], data['close'], period)
        
        # 当前值
        current_price = close.iloc[-1]
        current_ma_short = ma_short[-1] if not np.isnan(ma_short[-1]) else current_price
        current_ma_long = ma_long[-1] if not np.isnan(ma_long[-1]) else current_price
        current_adx = adx[-1] if not np.isnan(adx[-1]) else 25
        
        # 趋势方向
        if current_price > current_ma_short > current_ma_long:
            trend_direction = 1  # 上升趋势
        elif current_price < current_ma_short < current_ma_long:
            trend_direction = -1  # 下降趋势
        else:
            trend_direction = 0  # 横盘
        
        return {
            'trend_direction': trend_direction,
            'trend_strength': current_adx,
            'price_vs_ma_short': (current_price - current_ma_short) / current_ma_short,
            'price_vs_ma_long': (current_price - current_ma_long) / current_ma_long
        }
    
    @classmethod
    def calculate_all_indicators(cls, data: pd.DataFrame) -> pd.DataFrame:
        """计算所有常用技术指标"""
        df = data.copy()
        
        try:
            # 移动平均线
            df['sma_20'] = cls.sma(df['close'], 20)
            df['sma_50'] = cls.sma(df['close'], 50)
            df['ema_12'] = cls.ema(df['close'], 12)
            df['ema_26'] = cls.ema(df['close'], 26)
            
            # RSI
            df['rsi'] = cls.rsi(df['close'])
            
            # MACD
            macd_line, signal_line, histogram = cls.macd(df['close'])
            df['macd'] = macd_line
            df['macd_signal'] = signal_line
            df['macd_histogram'] = histogram
            
            # 布林带
            bb_upper, bb_middle, bb_lower = cls.bollinger_bands(df['close'])
            df['bb_upper'] = bb_upper
            df['bb_middle'] = bb_middle
            df['bb_lower'] = bb_lower
            
            # 随机指标
            stoch_k, stoch_d = cls.stochastic(df['high'], df['low'], df['close'])
            df['stoch_k'] = stoch_k
            df['stoch_d'] = stoch_d
            
            # ATR
            df['atr'] = cls.atr(df['high'], df['low'], df['close'])
            
            # ADX
            df['adx'] = cls.adx(df['high'], df['low'], df['close'])
            
            # 形态识别
            df['doji'] = cls.doji_pattern(df['open'], df['high'], df['low'], df['close'])
            df['hammer'] = cls.hammer_pattern(df['open'], df['high'], df['low'], df['close'])
            df['engulfing'] = cls.engulfing_pattern(df['open'], df['high'], df['low'], df['close'])
            
            # 波动率
            df['volatility'] = cls.calculate_volatility(df['close'])
            
            logger.info(f"技术指标计算完成，共{len(df.columns)}个指标")
            return df
            
        except Exception as e:
            logger.error(f"计算技术指标失败: {e}")
            return df
