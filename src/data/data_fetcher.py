"""
数据获取模块
负责从币安获取实时和历史数据
"""

import time
import logging
from typing import Dict, List, Optional
from datetime import datetime, timedelta
import pandas as pd
from src.api.binance_client import BinanceClient
from config.config import config

logger = logging.getLogger(__name__)


class DataFetcher:
    """数据获取器"""
    
    def __init__(self, client: BinanceClient):
        """初始化数据获取器"""
        self.client = client
        self.cache = {}
        self.cache_timeout = 60  # 缓存超时时间(秒)
        
    def get_realtime_price(self, symbol: str, use_cache: bool = True) -> float:
        """获取实时价格"""
        cache_key = f"price_{symbol}"
        
        if use_cache and cache_key in self.cache:
            cached_data, timestamp = self.cache[cache_key]
            if time.time() - timestamp < 5:  # 价格缓存5秒
                return cached_data
        
        try:
            price = self.client.get_ticker_price(symbol)
            if use_cache:
                self.cache[cache_key] = (price, time.time())
            return price
        except Exception as e:
            logger.error(f"获取实时价格失败 {symbol}: {e}")
            raise
    
    def get_realtime_prices(self, symbols: List[str]) -> Dict[str, float]:
        """批量获取实时价格"""
        prices = {}
        for symbol in symbols:
            try:
                prices[symbol] = self.get_realtime_price(symbol)
            except Exception as e:
                logger.warning(f"获取价格失败 {symbol}: {e}")
                prices[symbol] = None
        return prices
    
    def get_orderbook_data(self, symbol: str, depth: int = 20) -> Dict:
        """获取订单簿数据"""
        cache_key = f"orderbook_{symbol}_{depth}"
        
        if cache_key in self.cache:
            cached_data, timestamp = self.cache[cache_key]
            if time.time() - timestamp < 2:  # 订单簿缓存2秒
                return cached_data
        
        try:
            orderbook = self.client.get_orderbook(symbol, limit=depth)
            self.cache[cache_key] = (orderbook, time.time())
            return orderbook
        except Exception as e:
            logger.error(f"获取订单簿失败 {symbol}: {e}")
            raise
    
    def get_historical_data(self, symbol: str, interval: str, 
                          days: int = 30, limit: int = 1000) -> pd.DataFrame:
        """获取历史K线数据"""
        try:
            # 计算开始时间
            end_time = int(time.time() * 1000)
            start_time = end_time - (days * 24 * 60 * 60 * 1000)
            
            df = self.client.get_klines(
                symbol=symbol,
                interval=interval,
                limit=limit,
                start_time=start_time,
                end_time=end_time
            )
            
            logger.info(f"获取历史数据成功 {symbol} {interval} {len(df)}条")
            return df
            
        except Exception as e:
            logger.error(f"获取历史数据失败 {symbol}: {e}")
            raise
    
    def get_recent_trades(self, symbol: str, limit: int = 100) -> List[Dict]:
        """获取最近交易记录"""
        try:
            trades = self.client.client.get_recent_trades(symbol=symbol, limit=limit)
            formatted_trades = []
            
            for trade in trades:
                formatted_trades.append({
                    'id': trade['id'],
                    'price': float(trade['price']),
                    'quantity': float(trade['qty']),
                    'time': trade['time'],
                    'is_buyer_maker': trade['isBuyerMaker']
                })
            
            return formatted_trades
            
        except Exception as e:
            logger.error(f"获取交易记录失败 {symbol}: {e}")
            raise
    
    def get_24hr_ticker(self, symbol: str) -> Dict:
        """获取24小时价格统计"""
        try:
            ticker = self.client.client.get_ticker(symbol=symbol)
            return {
                'symbol': ticker['symbol'],
                'price_change': float(ticker['priceChange']),
                'price_change_percent': float(ticker['priceChangePercent']),
                'weighted_avg_price': float(ticker['weightedAvgPrice']),
                'prev_close_price': float(ticker['prevClosePrice']),
                'last_price': float(ticker['lastPrice']),
                'bid_price': float(ticker['bidPrice']),
                'ask_price': float(ticker['askPrice']),
                'open_price': float(ticker['openPrice']),
                'high_price': float(ticker['highPrice']),
                'low_price': float(ticker['lowPrice']),
                'volume': float(ticker['volume']),
                'quote_volume': float(ticker['quoteVolume']),
                'open_time': ticker['openTime'],
                'close_time': ticker['closeTime'],
                'count': ticker['count']
            }
        except Exception as e:
            logger.error(f"获取24小时统计失败 {symbol}: {e}")
            raise
    
    def get_market_depth_analysis(self, symbol: str) -> Dict:
        """获取市场深度分析"""
        try:
            orderbook = self.get_orderbook_data(symbol, depth=100)
            
            bids = orderbook['bids']
            asks = orderbook['asks']
            
            # 计算买卖压力
            bid_volume = sum([bid[1] for bid in bids[:20]])
            ask_volume = sum([ask[1] for ask in asks[:20]])
            
            # 计算价差
            best_bid = bids[0][0] if bids else 0
            best_ask = asks[0][0] if asks else 0
            spread = best_ask - best_bid if best_bid and best_ask else 0
            spread_pct = (spread / best_ask * 100) if best_ask else 0
            
            return {
                'symbol': symbol,
                'best_bid': best_bid,
                'best_ask': best_ask,
                'spread': spread,
                'spread_percent': spread_pct,
                'bid_volume_20': bid_volume,
                'ask_volume_20': ask_volume,
                'volume_ratio': bid_volume / ask_volume if ask_volume > 0 else 0,
                'timestamp': time.time()
            }
            
        except Exception as e:
            logger.error(f"市场深度分析失败 {symbol}: {e}")
            raise
    
    def get_multi_timeframe_data(self, symbol: str, 
                               intervals: List[str] = ['1m', '5m', '15m', '1h', '4h', '1d']) -> Dict[str, pd.DataFrame]:
        """获取多时间框架数据"""
        data = {}
        for interval in intervals:
            try:
                df = self.get_historical_data(symbol, interval, days=30)
                data[interval] = df
                logger.info(f"获取 {symbol} {interval} 数据成功: {len(df)}条")
            except Exception as e:
                logger.warning(f"获取 {symbol} {interval} 数据失败: {e}")
                data[interval] = pd.DataFrame()
        
        return data
    
    def clear_cache(self):
        """清理缓存"""
        self.cache.clear()
        logger.info("数据缓存已清理")
    
    def get_cache_info(self) -> Dict:
        """获取缓存信息"""
        current_time = time.time()
        valid_cache = 0
        expired_cache = 0
        
        for key, (data, timestamp) in self.cache.items():
            if current_time - timestamp < self.cache_timeout:
                valid_cache += 1
            else:
                expired_cache += 1
        
        return {
            'total_cache': len(self.cache),
            'valid_cache': valid_cache,
            'expired_cache': expired_cache,
            'cache_timeout': self.cache_timeout
        }
