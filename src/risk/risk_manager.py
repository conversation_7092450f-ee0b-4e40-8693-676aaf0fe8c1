"""
风险管理模块
负责监控和控制交易风险，包括仓位管理、止损止盈、最大回撤控制等
"""

import time
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from src.api.binance_client import BinanceClient
from src.strategies.base_strategy import Position, TradingSignal
from config.config import config

logger = logging.getLogger(__name__)


class RiskMetrics:
    """风险指标"""
    def __init__(self):
        self.total_exposure = 0.0
        self.max_drawdown = 0.0
        self.current_drawdown = 0.0
        self.daily_pnl = 0.0
        self.var_95 = 0.0  # 95% VaR
        self.sharpe_ratio = 0.0
        self.volatility = 0.0
        self.correlation_risk = 0.0
        self.last_update = time.time()


class RiskManager:
    """风险管理器"""
    
    def __init__(self, client: BinanceClient):
        """初始化风险管理器"""
        self.client = client
        self.config = config.RISK_MANAGEMENT_CONFIG
        
        # 风险限制
        self.max_daily_loss = self.config['max_daily_loss']
        self.max_drawdown = self.config['max_drawdown']
        self.position_size_limit = self.config['position_size_limit']
        self.correlation_limit = self.config['correlation_limit']
        self.volatility_threshold = self.config['volatility_threshold']
        
        # 风险状态
        self.risk_metrics = RiskMetrics()
        self.daily_start_balance = 0.0
        self.peak_balance = 0.0
        self.positions_history: List[Dict] = []
        self.pnl_history: List[Dict] = []
        
        # 紧急停止标志
        self.emergency_stop = False
        self.stop_reason = ""
        
        logger.info("风险管理器初始化完成")
    
    def check_signal_risk(self, signal: TradingSignal, current_positions: Dict[str, Position]) -> Tuple[bool, str]:
        """检查交易信号的风险"""
        try:
            # 1. 检查紧急停止状态
            if self.emergency_stop:
                return False, f"紧急停止状态: {self.stop_reason}"
            
            # 2. 检查日损失限制
            if not self._check_daily_loss_limit():
                return False, "超过日损失限制"
            
            # 3. 检查最大回撤限制
            if not self._check_max_drawdown_limit():
                return False, "超过最大回撤限制"
            
            # 4. 检查仓位大小限制
            if not self._check_position_size_limit(signal):
                return False, "超过单仓位大小限制"
            
            # 5. 检查总敞口限制
            if not self._check_total_exposure_limit(signal, current_positions):
                return False, "超过总敞口限制"
            
            # 6. 检查相关性风险
            if not self._check_correlation_risk(signal, current_positions):
                return False, "相关性风险过高"
            
            # 7. 检查波动率风险
            if not self._check_volatility_risk(signal):
                return False, "波动率风险过高"
            
            return True, "风险检查通过"
            
        except Exception as e:
            logger.error(f"风险检查失败: {e}")
            return False, f"风险检查错误: {e}"
    
    def update_risk_metrics(self, positions: Dict[str, Position], current_prices: Dict[str, float]):
        """更新风险指标"""
        try:
            # 获取账户信息
            account_info = self.client.get_account_info()
            current_balance = account_info['total_balance_usdt']
            
            # 初始化每日起始余额
            if self.daily_start_balance == 0:
                self.daily_start_balance = current_balance
                self.peak_balance = current_balance
            
            # 更新峰值余额
            if current_balance > self.peak_balance:
                self.peak_balance = current_balance
            
            # 计算当前回撤
            self.risk_metrics.current_drawdown = (self.peak_balance - current_balance) / self.peak_balance
            
            # 更新最大回撤
            if self.risk_metrics.current_drawdown > self.risk_metrics.max_drawdown:
                self.risk_metrics.max_drawdown = self.risk_metrics.current_drawdown
            
            # 计算日盈亏
            self.risk_metrics.daily_pnl = current_balance - self.daily_start_balance
            
            # 计算总敞口
            self.risk_metrics.total_exposure = self._calculate_total_exposure(positions, current_prices)
            
            # 更新PnL历史
            self.pnl_history.append({
                'timestamp': time.time(),
                'balance': current_balance,
                'daily_pnl': self.risk_metrics.daily_pnl,
                'drawdown': self.risk_metrics.current_drawdown
            })
            
            # 保持历史记录在合理范围内
            if len(self.pnl_history) > 1000:
                self.pnl_history = self.pnl_history[-1000:]
            
            # 计算其他风险指标
            self._calculate_advanced_metrics()
            
            self.risk_metrics.last_update = time.time()
            
            # 检查是否需要紧急停止
            self._check_emergency_stop()
            
        except Exception as e:
            logger.error(f"更新风险指标失败: {e}")
    
    def _check_daily_loss_limit(self) -> bool:
        """检查日损失限制"""
        daily_loss_pct = abs(self.risk_metrics.daily_pnl) / self.daily_start_balance if self.daily_start_balance > 0 else 0
        return daily_loss_pct <= self.max_daily_loss
    
    def _check_max_drawdown_limit(self) -> bool:
        """检查最大回撤限制"""
        return self.risk_metrics.current_drawdown <= self.max_drawdown
    
    def _check_position_size_limit(self, signal: TradingSignal) -> bool:
        """检查单仓位大小限制"""
        try:
            account_info = self.client.get_account_info()
            total_balance = account_info['total_balance_usdt']
            
            position_value = signal.price * signal.quantity
            position_pct = position_value / total_balance
            
            return position_pct <= self.position_size_limit
            
        except Exception as e:
            logger.error(f"检查仓位大小限制失败: {e}")
            return False
    
    def _check_total_exposure_limit(self, signal: TradingSignal, positions: Dict[str, Position]) -> bool:
        """检查总敞口限制"""
        try:
            account_info = self.client.get_account_info()
            total_balance = account_info['total_balance_usdt']
            
            # 计算当前总敞口
            current_exposure = 0.0
            for position in positions.values():
                current_exposure += position.quantity * position.entry_price
            
            # 加上新信号的敞口
            new_exposure = signal.price * signal.quantity
            total_exposure = current_exposure + new_exposure
            
            exposure_pct = total_exposure / total_balance
            
            # 总敞口不应超过账户余额的80%
            return exposure_pct <= 0.8
            
        except Exception as e:
            logger.error(f"检查总敞口限制失败: {e}")
            return False
    
    def _check_correlation_risk(self, signal: TradingSignal, positions: Dict[str, Position]) -> bool:
        """检查相关性风险"""
        try:
            # 简化的相关性检查：避免同时持有过多相关资产
            signal_asset = signal.symbol.replace('USDT', '')
            
            # 定义相关资产组
            correlated_groups = {
                'BTC': ['BTC'],
                'ETH': ['ETH'],
                'SOL': ['SOL'],
                'ALTCOINS': ['ADA', 'DOT', 'LINK', 'UNI']
            }
            
            # 找到信号资产所属的组
            signal_group = None
            for group, assets in correlated_groups.items():
                if signal_asset in assets:
                    signal_group = group
                    break
            
            if not signal_group:
                return True  # 未知资产，允许交易
            
            # 计算同组资产的总敞口
            group_exposure = 0.0
            for position in positions.values():
                position_asset = position.symbol.replace('USDT', '')
                if position_asset in correlated_groups.get(signal_group, []):
                    group_exposure += position.quantity * position.entry_price
            
            # 加上新信号的敞口
            group_exposure += signal.price * signal.quantity
            
            # 获取总余额
            account_info = self.client.get_account_info()
            total_balance = account_info['total_balance_usdt']
            
            group_exposure_pct = group_exposure / total_balance
            
            # 单个相关组的敞口不应超过30%
            return group_exposure_pct <= 0.3
            
        except Exception as e:
            logger.error(f"检查相关性风险失败: {e}")
            return True  # 出错时允许交易
    
    def _check_volatility_risk(self, signal: TradingSignal) -> bool:
        """检查波动率风险"""
        try:
            # 这里可以实现更复杂的波动率检查
            # 暂时返回True，表示通过检查
            return True
            
        except Exception as e:
            logger.error(f"检查波动率风险失败: {e}")
            return True
    
    def _calculate_total_exposure(self, positions: Dict[str, Position], current_prices: Dict[str, float]) -> float:
        """计算总敞口"""
        total_exposure = 0.0
        
        for symbol, position in positions.items():
            current_price = current_prices.get(symbol, position.entry_price)
            exposure = position.quantity * current_price
            total_exposure += exposure
        
        return total_exposure
    
    def _calculate_advanced_metrics(self):
        """计算高级风险指标"""
        try:
            if len(self.pnl_history) < 30:
                return
            
            # 获取最近30天的数据
            recent_data = self.pnl_history[-30:]
            daily_returns = []
            
            for i in range(1, len(recent_data)):
                prev_balance = recent_data[i-1]['balance']
                curr_balance = recent_data[i]['balance']
                daily_return = (curr_balance - prev_balance) / prev_balance if prev_balance > 0 else 0
                daily_returns.append(daily_return)
            
            if not daily_returns:
                return
            
            # 计算波动率
            self.risk_metrics.volatility = np.std(daily_returns) * np.sqrt(252)  # 年化波动率
            
            # 计算夏普比率
            avg_return = np.mean(daily_returns) * 252  # 年化收益率
            risk_free_rate = 0.02  # 假设无风险利率2%
            
            if self.risk_metrics.volatility > 0:
                self.risk_metrics.sharpe_ratio = (avg_return - risk_free_rate) / self.risk_metrics.volatility
            
            # 计算VaR (95%)
            if len(daily_returns) >= 20:
                self.risk_metrics.var_95 = np.percentile(daily_returns, 5)
            
        except Exception as e:
            logger.error(f"计算高级风险指标失败: {e}")
    
    def _check_emergency_stop(self):
        """检查是否需要紧急停止"""
        try:
            # 检查日损失是否过大
            if self.risk_metrics.daily_pnl < 0:
                daily_loss_pct = abs(self.risk_metrics.daily_pnl) / self.daily_start_balance
                if daily_loss_pct > self.max_daily_loss * 1.5:  # 超过限制的1.5倍
                    self.emergency_stop = True
                    self.stop_reason = f"日损失过大: {daily_loss_pct:.2%}"
                    logger.critical(f"触发紧急停止: {self.stop_reason}")
                    return
            
            # 检查回撤是否过大
            if self.risk_metrics.current_drawdown > self.max_drawdown * 1.2:  # 超过限制的1.2倍
                self.emergency_stop = True
                self.stop_reason = f"回撤过大: {self.risk_metrics.current_drawdown:.2%}"
                logger.critical(f"触发紧急停止: {self.stop_reason}")
                return
            
        except Exception as e:
            logger.error(f"检查紧急停止失败: {e}")
    
    def reset_daily_metrics(self):
        """重置日指标"""
        try:
            account_info = self.client.get_account_info()
            self.daily_start_balance = account_info['total_balance_usdt']
            self.risk_metrics.daily_pnl = 0.0
            
            logger.info("日风险指标已重置")
            
        except Exception as e:
            logger.error(f"重置日指标失败: {e}")
    
    def get_risk_report(self) -> Dict[str, Any]:
        """获取风险报告"""
        return {
            'emergency_stop': self.emergency_stop,
            'stop_reason': self.stop_reason,
            'risk_metrics': {
                'total_exposure': self.risk_metrics.total_exposure,
                'max_drawdown': self.risk_metrics.max_drawdown,
                'current_drawdown': self.risk_metrics.current_drawdown,
                'daily_pnl': self.risk_metrics.daily_pnl,
                'var_95': self.risk_metrics.var_95,
                'sharpe_ratio': self.risk_metrics.sharpe_ratio,
                'volatility': self.risk_metrics.volatility,
                'last_update': self.risk_metrics.last_update
            },
            'risk_limits': {
                'max_daily_loss': self.max_daily_loss,
                'max_drawdown': self.max_drawdown,
                'position_size_limit': self.position_size_limit,
                'correlation_limit': self.correlation_limit
            },
            'account_status': {
                'daily_start_balance': self.daily_start_balance,
                'peak_balance': self.peak_balance,
                'pnl_history_length': len(self.pnl_history)
            }
        }
    
    def force_stop(self, reason: str):
        """强制停止交易"""
        self.emergency_stop = True
        self.stop_reason = reason
        logger.critical(f"强制停止交易: {reason}")
    
    def resume_trading(self):
        """恢复交易"""
        self.emergency_stop = False
        self.stop_reason = ""
        logger.info("交易已恢复")
