# 币安量化交易自动化系统

## 项目概述
这是一个基于Python的币安(Binance)量化交易自动化项目，实现了多种交易策略的自动化执行。

## 支持的交易品种
- BTC (比特币)
- ETH (以太坊)
- SOL (Solana)

## 实现的交易策略
1. **DOJIplus趋势策略** - 基于趋势分析的交易策略
2. **Triangle Theme三角套利** - 三角套利策略，利用不同交易对之间的价格差异
3. **MatrixHFGT网格交易** - 网格交易策略，在价格区间内进行高频买卖

## 项目结构
```
quant/
├── config/                 # 配置文件目录
│   ├── __init__.py
│   ├── config.py          # 主配置文件
│   └── .env.example       # 环境变量示例
├── src/                   # 源代码目录
│   ├── __init__.py
│   ├── api/               # API接口模块
│   │   ├── __init__.py
│   │   └── binance_client.py
│   ├── data/              # 数据处理模块
│   │   ├── __init__.py
│   │   ├── data_fetcher.py
│   │   └── indicators.py
│   ├── strategies/        # 交易策略模块
│   │   ├── __init__.py
│   │   ├── base_strategy.py
│   │   ├── doji_plus.py
│   │   ├── triangle_arbitrage.py
│   │   └── matrix_grid.py
│   ├── risk/              # 风险管理模块
│   │   ├── __init__.py
│   │   └── risk_manager.py
│   ├── backtest/          # 回测模块
│   │   ├── __init__.py
│   │   └── backtester.py
│   └── utils/             # 工具模块
│       ├── __init__.py
│       ├── logger.py
│       └── monitor.py
├── tests/                 # 测试目录
├── logs/                  # 日志目录
├── data/                  # 数据存储目录
├── main.py               # 主程序入口
├── requirements.txt      # 依赖包列表
└── README.md            # 项目说明

```

## 安装和配置

### 1. 环境设置
```bash
# 创建conda环境
conda create -n quant python=3.11 -y
conda activate quant

# 安装依赖
pip install -r requirements.txt
conda install -c conda-forge ta-lib -y
```

### 2. 配置API密钥
1. 复制 `config/.env.example` 为 `config/.env`
2. 填入您的币安API密钥和密钥

### 3. 运行程序
```bash
python main.py
```

## 功能特性
- ✅ 币安API集成
- ✅ 实时数据获取
- ✅ 多策略并行运行
- ✅ 风险管理系统
- ✅ 回测功能
- ✅ 日志记录和监控
- ✅ 配置文件管理

## 风险提示
⚠️ **重要提醒**：
- 量化交易存在风险，请谨慎使用
- 建议先在测试环境中运行
- 请合理设置止损和仓位管理
- 不要投入超过您承受能力的资金

## 许可证
MIT License