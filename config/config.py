"""
配置管理模块
负责加载和管理所有配置参数
"""

import os
from typing import Dict, Any, List
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(os.path.join(os.path.dirname(__file__), '.env'))


class Config:
    """配置类，管理所有系统配置"""
    
    # 币安API配置
    BINANCE_API_KEY = os.getenv('BINANCE_API_KEY', '')
    BINANCE_SECRET_KEY = os.getenv('BINANCE_SECRET_KEY', '')
    BINANCE_TESTNET = os.getenv('BINANCE_TESTNET', 'True').lower() == 'true'
    
    # 交易配置
    DEFAULT_QUOTE_ASSET = os.getenv('DEFAULT_QUOTE_ASSET', 'USDT')
    MAX_POSITION_SIZE = float(os.getenv('MAX_POSITION_SIZE', '1000'))
    RISK_PERCENTAGE = float(os.getenv('RISK_PERCENTAGE', '0.02'))
    
    # 支持的交易对
    TRADING_PAIRS = [
        'BTCUSDT',
        'ETHUSDT', 
        'SOLUSDT'
    ]
    
    # 策略配置
    ENABLE_DOJI_STRATEGY = os.getenv('ENABLE_DOJI_STRATEGY', 'True').lower() == 'true'
    ENABLE_TRIANGLE_ARBITRAGE = os.getenv('ENABLE_TRIANGLE_ARBITRAGE', 'True').lower() == 'true'
    ENABLE_GRID_TRADING = os.getenv('ENABLE_GRID_TRADING', 'True').lower() == 'true'
    
    # DOJIplus策略参数
    DOJI_STRATEGY_CONFIG = {
        'timeframe': '1h',
        'rsi_period': 14,
        'rsi_overbought': 70,
        'rsi_oversold': 30,
        'ma_period': 20,
        'stop_loss_pct': 0.02,
        'take_profit_pct': 0.04
    }
    
    # 三角套利策略参数
    TRIANGLE_ARBITRAGE_CONFIG = {
        'min_profit_threshold': 0.001,  # 最小利润阈值 0.1%
        'max_slippage': 0.002,          # 最大滑点 0.2%
        'check_interval': 5,            # 检查间隔(秒)
        'triangles': [
            ['BTC', 'ETH', 'USDT'],
            ['BTC', 'SOL', 'USDT'],
            ['ETH', 'SOL', 'USDT']
        ]
    }
    
    # 网格交易策略参数
    GRID_TRADING_CONFIG = {
        'grid_count': 10,               # 网格数量
        'price_range_pct': 0.1,         # 价格范围百分比 10%
        'order_amount_pct': 0.1,        # 每单金额占总资金比例
        'rebalance_threshold': 0.05,    # 重新平衡阈值
        'max_orders': 20                # 最大订单数
    }
    
    # 风险管理配置
    RISK_MANAGEMENT_CONFIG = {
        'max_daily_loss': 0.05,         # 最大日损失 5%
        'max_drawdown': 0.1,            # 最大回撤 10%
        'position_size_limit': 0.2,     # 单个仓位限制 20%
        'correlation_limit': 0.7,       # 相关性限制
        'volatility_threshold': 0.3     # 波动率阈值
    }
    
    # 日志配置
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE_PATH = os.getenv('LOG_FILE_PATH', 'logs/trading.log')
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    LOG_MAX_BYTES = 10 * 1024 * 1024  # 10MB
    LOG_BACKUP_COUNT = 5
    
    # 数据库配置
    DATABASE_URL = os.getenv('DATABASE_URL', 'sqlite:///data/trading.db')
    
    # 监控配置
    ENABLE_MONITORING = os.getenv('ENABLE_MONITORING', 'True').lower() == 'true'
    MONITOR_INTERVAL = int(os.getenv('MONITOR_INTERVAL', '60'))
    
    # 回测配置
    BACKTEST_CONFIG = {
        'start_date': '2023-01-01',
        'end_date': '2024-12-31',
        'initial_balance': 10000,
        'commission': 0.001,            # 手续费 0.1%
        'slippage': 0.0005             # 滑点 0.05%
    }
    
    @classmethod
    def validate_config(cls) -> bool:
        """验证配置是否有效"""
        if not cls.BINANCE_API_KEY or not cls.BINANCE_SECRET_KEY:
            print("警告: 币安API密钥未配置")
            return False
        
        if cls.RISK_PERCENTAGE <= 0 or cls.RISK_PERCENTAGE > 0.1:
            print("警告: 风险百分比设置不合理")
            return False
            
        return True
    
    @classmethod
    def get_strategy_config(cls, strategy_name: str) -> Dict[str, Any]:
        """获取特定策略的配置"""
        config_map = {
            'doji_plus': cls.DOJI_STRATEGY_CONFIG,
            'triangle_arbitrage': cls.TRIANGLE_ARBITRAGE_CONFIG,
            'grid_trading': cls.GRID_TRADING_CONFIG
        }
        return config_map.get(strategy_name, {})
    
    @classmethod
    def get_enabled_strategies(cls) -> List[str]:
        """获取启用的策略列表"""
        enabled = []
        if cls.ENABLE_DOJI_STRATEGY:
            enabled.append('doji_plus')
        if cls.ENABLE_TRIANGLE_ARBITRAGE:
            enabled.append('triangle_arbitrage')
        if cls.ENABLE_GRID_TRADING:
            enabled.append('grid_trading')
        return enabled


# 创建全局配置实例
config = Config()
