#!/usr/bin/env python3
"""
全策略测试脚本
测试所有三种策略的功能和表现，包括详细的回测分析和可视化
"""

import sys
import os
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import seaborn as sns
from matplotlib.patches import Rectangle

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.api.binance_client import BinanceClient
from src.data.data_fetcher import DataFetcher
from src.strategies.doji_plus import DojiPlusStrategy
from src.strategies.triangle_arbitrage import TriangleArbitrageStrategy
from src.strategies.matrix_grid import MatrixGridStrategy
from src.backtest.backtester import Backtester, BacktestResult
from config.config import config
from src.utils.logger import get_logger

logger = get_logger(__name__)

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")


def test_doji_plus_strategy():
    """测试DOJIplus趋势策略"""
    print("=" * 60)
    print("测试 DOJIplus 趋势策略")
    print("=" * 60)
    
    try:
        client = BinanceClient()
        data_fetcher = DataFetcher(client)
        
        # 创建策略
        strategy_config = config.get_strategy_config('doji_plus')
        strategy = DojiPlusStrategy(client, data_fetcher, strategy_config)
        
        print(f"✓ 策略创建成功")
        print(f"  配置: {strategy_config}")
        
        # 获取测试数据
        data = data_fetcher.get_historical_data('BTCUSDT', '1h', days=30)
        print(f"✓ 获取历史数据: {len(data)} 条")
        
        # 生成信号
        signals = strategy.generate_signals('BTCUSDT', data)
        print(f"✓ 信号生成: {len(signals)} 个信号")
        
        # 获取策略状态
        status = strategy.get_strategy_status('BTCUSDT')
        print(f"✓ 策略状态获取成功")
        print(f"  当前价格: {status['current_price']:.2f}")
        print(f"  RSI: {status['indicators']['rsi']:.2f}")
        print(f"  MACD: {status['indicators']['macd']:.6f}")
        
        # 简单回测
        backtester = Backtester(data_fetcher)
        result = backtester.run_backtest(strategy, 'BTCUSDT', '2024-01-01', '2024-12-31', '1h')
        
        print(f"✓ 回测完成")
        print(f"  总收益: {result.total_return:.2f} USDT ({result.total_return_pct:.2%})")
        print(f"  交易次数: {result.total_trades}")
        print(f"  胜率: {result.win_rate:.2%}")
        print(f"  最大回撤: {result.max_drawdown:.2%}")
        
        return True
        
    except Exception as e:
        print(f"✗ DOJIplus策略测试失败: {e}")
        return False


def test_triangle_arbitrage_strategy():
    """测试三角套利策略"""
    print("=" * 60)
    print("测试 Triangle Theme 三角套利策略")
    print("=" * 60)
    
    try:
        client = BinanceClient()
        data_fetcher = DataFetcher(client)
        
        # 创建策略
        strategy_config = config.get_strategy_config('triangle_arbitrage')
        strategy = TriangleArbitrageStrategy(client, data_fetcher, strategy_config)
        
        print(f"✓ 策略创建成功")
        print(f"  监控三角组合: {strategy.triangles}")
        print(f"  最小利润阈值: {strategy.min_profit_threshold:.3f}")
        
        # 获取测试数据
        data = data_fetcher.get_historical_data('BTCUSDT', '1h', days=7)
        print(f"✓ 获取历史数据: {len(data)} 条")
        
        # 生成信号 (三角套利基于实时价格，不依赖历史数据)
        signals = strategy.generate_signals('BTCUSDT', data)
        print(f"✓ 信号生成: {len(signals)} 个套利信号")
        
        # 获取套利摘要
        summary = strategy.get_arbitrage_summary()
        print(f"✓ 套利摘要:")
        print(f"  监控三角组合: {summary['triangles_monitored']} 个")
        print(f"  套利机会: {summary['total_opportunities']} 个")
        print(f"  平均利润率: {summary['avg_profit_rate']:.4f}")
        
        return True
        
    except Exception as e:
        print(f"✗ 三角套利策略测试失败: {e}")
        return False


def test_matrix_grid_strategy():
    """测试网格交易策略"""
    print("=" * 60)
    print("测试 MatrixHFGT 网格交易策略")
    print("=" * 60)
    
    try:
        client = BinanceClient()
        data_fetcher = DataFetcher(client)
        
        # 创建策略
        strategy_config = config.get_strategy_config('grid_trading')
        strategy = MatrixGridStrategy(client, data_fetcher, strategy_config)
        
        print(f"✓ 策略创建成功")
        print(f"  网格数量: {strategy.grid_count}")
        print(f"  价格范围: {strategy.price_range_pct:.1%}")
        print(f"  每单金额比例: {strategy.order_amount_pct:.1%}")
        
        # 获取测试数据
        data = data_fetcher.get_historical_data('BTCUSDT', '1h', days=7)
        print(f"✓ 获取历史数据: {len(data)} 条")
        
        # 生成信号
        signals = strategy.generate_signals('BTCUSDT', data)
        print(f"✓ 信号生成: {len(signals)} 个网格信号")
        
        # 获取网格状态
        if 'BTCUSDT' in strategy.grids:
            status = strategy.get_grid_status('BTCUSDT')
            print(f"✓ 网格状态:")
            print(f"  中心价格: {status['grid_info']['center_price']:.2f}")
            print(f"  价格范围: {status['grid_info']['lower_price']:.2f} - {status['grid_info']['upper_price']:.2f}")
            print(f"  网格间距: {status['grid_info']['grid_spacing']:.2f}")
            print(f"  活跃订单: {status['active_orders']} 个")
        
        return True
        
    except Exception as e:
        print(f"✗ 网格交易策略测试失败: {e}")
        return False


def test_real_time_signals():
    """测试实时信号生成"""
    print("=" * 60)
    print("测试实时信号生成")
    print("=" * 60)
    
    try:
        client = BinanceClient()
        data_fetcher = DataFetcher(client)
        
        # 创建所有策略
        strategies = {}
        
        # DOJIplus策略
        doji_config = config.get_strategy_config('doji_plus')
        strategies['DOJIplus'] = DojiPlusStrategy(client, data_fetcher, doji_config)
        
        # 三角套利策略
        triangle_config = config.get_strategy_config('triangle_arbitrage')
        strategies['TriangleArbitrage'] = TriangleArbitrageStrategy(client, data_fetcher, triangle_config)
        
        # 网格交易策略
        grid_config = config.get_strategy_config('grid_trading')
        strategies['MatrixGrid'] = MatrixGridStrategy(client, data_fetcher, grid_config)
        
        print(f"✓ 创建了 {len(strategies)} 个策略")
        
        # 获取实时数据
        symbols = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT']
        
        for symbol in symbols:
            print(f"\n--- {symbol} 信号测试 ---")
            
            # 获取历史数据
            data = data_fetcher.get_historical_data(symbol, '1h', days=30)
            current_price = data_fetcher.get_realtime_price(symbol)
            
            print(f"当前价格: {current_price:.2f}")
            
            # 测试每个策略
            for name, strategy in strategies.items():
                try:
                    signals = strategy.generate_signals(symbol, data)
                    print(f"  {name}: {len(signals)} 个信号")
                    
                    for signal in signals:
                        print(f"    - {signal.signal_type.value} @ {signal.price:.2f} "
                              f"(置信度: {signal.confidence:.2f}) - {signal.reason}")
                        
                except Exception as e:
                    print(f"  {name}: 信号生成失败 - {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ 实时信号测试失败: {e}")
        return False


def comprehensive_backtest():
    """全面的多策略回测分析"""
    print("=" * 80)
    print("多策略综合回测分析")
    print("=" * 80)

    try:
        client = BinanceClient()
        data_fetcher = DataFetcher(client)
        backtester = Backtester(data_fetcher)

        # 定义策略配置
        strategies_config = {
            'DOJIplus': {
                'class': DojiPlusStrategy,
                'config': config.get_strategy_config('doji_plus'),
                'description': 'DOJIplus趋势策略 - 基于十字星形态和多技术指标'
            },
            'TriangleArbitrage': {
                'class': TriangleArbitrageStrategy,
                'config': config.get_strategy_config('triangle_arbitrage'),
                'description': '三角套利策略 - BTC/ETH/SOL三角套利'
            },
            'MatrixGrid': {
                'class': MatrixGridStrategy,
                'config': config.get_strategy_config('grid_trading'),
                'description': '网格交易策略 - 动态网格高频交易'
            }
        }

        # 回测参数
        test_symbols = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT']
        test_periods = [
            ('短期', '2024-10-01', '2024-12-31'),
            ('中期', '2024-07-01', '2024-12-31'),
            ('长期', '2024-01-01', '2024-12-31')
        ]

        all_results = {}

        print("开始多策略回测...")

        # 对每个策略进行回测
        for strategy_name, strategy_info in strategies_config.items():
            print(f"\n{'='*60}")
            print(f"回测策略: {strategy_name}")
            print(f"描述: {strategy_info['description']}")
            print(f"{'='*60}")

            strategy_results = {}

            try:
                # 创建策略实例
                strategy = strategy_info['class'](
                    client,
                    data_fetcher,
                    strategy_info['config']
                )

                # 对每个交易对进行回测
                for symbol in test_symbols:
                    print(f"\n--- 回测交易对: {symbol} ---")
                    symbol_results = {}

                    for period_name, start_date, end_date in test_periods:
                        print(f"  回测期间: {period_name} ({start_date} 到 {end_date})")

                        try:
                            # 根据策略选择合适的时间框架
                            timeframe = '1h'
                            if strategy_name == 'TriangleArbitrage':
                                timeframe = '5m'  # 三角套利需要更高频率
                            elif strategy_name == 'MatrixGrid':
                                timeframe = '15m'  # 网格交易中等频率

                            result = backtester.run_backtest(
                                strategy=strategy,
                                symbol=symbol,
                                start_date=start_date,
                                end_date=end_date,
                                timeframe=timeframe
                            )

                            symbol_results[period_name] = result
                            print(f"    ✓ 完成 - 收益率: {result.total_return_pct:.2%}, "
                                  f"胜率: {result.win_rate:.2%}, 最大回撤: {result.max_drawdown:.2%}")

                        except Exception as e:
                            print(f"    ✗ 失败: {e}")
                            logger.error(f"回测失败 {strategy_name}-{symbol}-{period_name}: {e}")

                    strategy_results[symbol] = symbol_results

                all_results[strategy_name] = strategy_results
                print(f"\n✓ {strategy_name} 策略回测完成")

            except Exception as e:
                print(f"✗ {strategy_name} 策略初始化失败: {e}")
                logger.error(f"策略初始化失败 {strategy_name}: {e}")

        # 生成综合分析报告
        if all_results:
            generate_comprehensive_report(all_results)
            create_performance_charts(all_results)

        return all_results

    except Exception as e:
        print(f"✗ 综合回测失败: {e}")
        logger.error(f"综合回测失败: {e}")
        return {}


def performance_comparison():
    """策略性能对比（保持原有功能）"""
    print("=" * 60)
    print("策略性能对比分析")
    print("=" * 60)

    try:
        client = BinanceClient()
        data_fetcher = DataFetcher(client)
        backtester = Backtester(data_fetcher)

        strategies = []
        results = []

        # 创建所有策略
        doji_config = config.get_strategy_config('doji_plus')
        doji_strategy = DojiPlusStrategy(client, data_fetcher, doji_config)
        strategies.append(('DOJIplus', doji_strategy))

        triangle_config = config.get_strategy_config('triangle_arbitrage')
        triangle_strategy = TriangleArbitrageStrategy(client, data_fetcher, triangle_config)
        strategies.append(('TriangleArbitrage', triangle_strategy))

        grid_config = config.get_strategy_config('grid_trading')
        grid_strategy = MatrixGridStrategy(client, data_fetcher, grid_config)
        strategies.append(('MatrixGrid', grid_strategy))

        print("开始策略对比回测...")

        # 运行回测
        for name, strategy in strategies:
            print(f"\n正在回测: {name}")
            try:
                # 根据策略选择时间框架
                timeframe = '1h'
                if name == 'TriangleArbitrage':
                    timeframe = '5m'
                elif name == 'MatrixGrid':
                    timeframe = '15m'

                result = backtester.run_backtest(
                    strategy=strategy,
                    symbol='BTCUSDT',
                    start_date='2024-07-01',
                    end_date='2024-12-31',
                    timeframe=timeframe
                )
                results.append((name, result))
                print(f"✓ {name} 回测完成")
            except Exception as e:
                print(f"✗ {name} 回测失败: {e}")

        # 显示对比结果
        if results:
            print("\n" + "=" * 100)
            print("策略性能对比结果")
            print("=" * 100)
            print(f"{'策略':<15} {'初始资金':<12} {'最终资金':<12} {'总收益':<12} {'收益率':<10} "
                  f"{'交易次数':<8} {'胜率':<8} {'最大回撤':<10} {'夏普比率':<10}")
            print("-" * 100)

            for name, result in results:
                print(f"{name:<15} "
                      f"{result.initial_balance:<12.2f} "
                      f"{result.final_balance:<12.2f} "
                      f"{result.total_return:<12.2f} "
                      f"{result.total_return_pct:<10.2%} "
                      f"{result.total_trades:<8} "
                      f"{result.win_rate:<8.2%} "
                      f"{result.max_drawdown:<10.2%} "
                      f"{result.sharpe_ratio:<10.2f}")

        return True

    except Exception as e:
        print(f"✗ 策略性能对比失败: {e}")
        return False


def generate_comprehensive_report(all_results: Dict[str, Any]):
    """生成综合分析报告"""
    print("\n" + "=" * 100)
    print("多策略综合回测报告")
    print("=" * 100)

    # 策略汇总表
    print("\n📊 策略性能汇总")
    print("-" * 100)
    print(f"{'策略':<15} {'交易对':<10} {'期间':<8} {'收益率':<10} {'胜率':<8} "
          f"{'交易次数':<8} {'最大回撤':<10} {'夏普比率':<10}")
    print("-" * 100)

    strategy_summary = {}

    for strategy_name, strategy_data in all_results.items():
        strategy_metrics = []

        for symbol, symbol_data in strategy_data.items():
            for period, result in symbol_data.items():
                if result:  # 确保结果不为空
                    print(f"{strategy_name:<15} {symbol:<10} {period:<8} "
                          f"{result.total_return_pct:<10.2%} {result.win_rate:<8.2%} "
                          f"{result.total_trades:<8} {result.max_drawdown:<10.2%} "
                          f"{result.sharpe_ratio:<10.2f}")

                    strategy_metrics.append({
                        'return_pct': result.total_return_pct,
                        'win_rate': result.win_rate,
                        'trades': result.total_trades,
                        'max_drawdown': result.max_drawdown,
                        'sharpe_ratio': result.sharpe_ratio
                    })

        # 计算策略平均表现
        if strategy_metrics:
            avg_return = np.mean([m['return_pct'] for m in strategy_metrics])
            avg_win_rate = np.mean([m['win_rate'] for m in strategy_metrics])
            total_trades = sum([m['trades'] for m in strategy_metrics])
            avg_drawdown = np.mean([m['max_drawdown'] for m in strategy_metrics])
            avg_sharpe = np.mean([m['sharpe_ratio'] for m in strategy_metrics])

            strategy_summary[strategy_name] = {
                'avg_return': avg_return,
                'avg_win_rate': avg_win_rate,
                'total_trades': total_trades,
                'avg_drawdown': avg_drawdown,
                'avg_sharpe': avg_sharpe
            }

    # 策略排名
    print("\n🏆 策略排名")
    print("-" * 60)

    # 按收益率排名
    sorted_by_return = sorted(strategy_summary.items(),
                             key=lambda x: x[1]['avg_return'], reverse=True)
    print("按平均收益率排名:")
    for i, (name, metrics) in enumerate(sorted_by_return, 1):
        print(f"  {i}. {name}: {metrics['avg_return']:.2%}")

    # 按夏普比率排名
    sorted_by_sharpe = sorted(strategy_summary.items(),
                             key=lambda x: x[1]['avg_sharpe'], reverse=True)
    print("\n按夏普比率排名:")
    for i, (name, metrics) in enumerate(sorted_by_sharpe, 1):
        print(f"  {i}. {name}: {metrics['avg_sharpe']:.2f}")

    # 风险分析
    print("\n⚠️  风险分析")
    print("-" * 60)
    for name, metrics in strategy_summary.items():
        risk_level = "低" if metrics['avg_drawdown'] < 0.05 else "中" if metrics['avg_drawdown'] < 0.15 else "高"
        print(f"{name}: 平均最大回撤 {metrics['avg_drawdown']:.2%} (风险等级: {risk_level})")

    # 保存报告到文件
    try:
        report_data = {
            'timestamp': datetime.now().isoformat(),
            'strategy_summary': strategy_summary,
            'detailed_results': all_results
        }

        with open('backtest_report.json', 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2, default=str)

        print(f"\n📄 详细报告已保存到: backtest_report.json")

    except Exception as e:
        print(f"保存报告失败: {e}")


def create_performance_charts(all_results: Dict[str, Any]):
    """创建性能对比图表"""
    try:
        # 准备数据
        strategies = list(all_results.keys())
        if not strategies:
            print("没有数据可用于生成图表")
            return

        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('多策略回测性能对比', fontsize=16, fontweight='bold')

        # 收集所有策略的平均指标
        strategy_metrics = {}
        for strategy_name, strategy_data in all_results.items():
            metrics = []
            for symbol_data in strategy_data.values():
                for result in symbol_data.values():
                    if result:
                        metrics.append({
                            'return_pct': result.total_return_pct,
                            'win_rate': result.win_rate,
                            'max_drawdown': result.max_drawdown,
                            'sharpe_ratio': result.sharpe_ratio
                        })

            if metrics:
                strategy_metrics[strategy_name] = {
                    'avg_return': np.mean([m['return_pct'] for m in metrics]),
                    'avg_win_rate': np.mean([m['win_rate'] for m in metrics]),
                    'avg_drawdown': np.mean([m['max_drawdown'] for m in metrics]),
                    'avg_sharpe': np.mean([m['sharpe_ratio'] for m in metrics])
                }

        if not strategy_metrics:
            print("没有有效的指标数据可用于生成图表")
            return

        # 1. 收益率对比
        returns = [strategy_metrics[s]['avg_return'] * 100 for s in strategies if s in strategy_metrics]
        axes[0, 0].bar(strategies, returns, color=['#1f77b4', '#ff7f0e', '#2ca02c'])
        axes[0, 0].set_title('平均收益率对比 (%)')
        axes[0, 0].set_ylabel('收益率 (%)')
        axes[0, 0].tick_params(axis='x', rotation=45)

        # 2. 胜率对比
        win_rates = [strategy_metrics[s]['avg_win_rate'] * 100 for s in strategies if s in strategy_metrics]
        axes[0, 1].bar(strategies, win_rates, color=['#d62728', '#9467bd', '#8c564b'])
        axes[0, 1].set_title('平均胜率对比 (%)')
        axes[0, 1].set_ylabel('胜率 (%)')
        axes[0, 1].tick_params(axis='x', rotation=45)

        # 3. 最大回撤对比
        drawdowns = [strategy_metrics[s]['avg_drawdown'] * 100 for s in strategies if s in strategy_metrics]
        axes[1, 0].bar(strategies, drawdowns, color=['#e377c2', '#7f7f7f', '#bcbd22'])
        axes[1, 0].set_title('平均最大回撤对比 (%)')
        axes[1, 0].set_ylabel('最大回撤 (%)')
        axes[1, 0].tick_params(axis='x', rotation=45)

        # 4. 夏普比率对比
        sharpe_ratios = [strategy_metrics[s]['avg_sharpe'] for s in strategies if s in strategy_metrics]
        axes[1, 1].bar(strategies, sharpe_ratios, color=['#17becf', '#ff9896', '#98df8a'])
        axes[1, 1].set_title('平均夏普比率对比')
        axes[1, 1].set_ylabel('夏普比率')
        axes[1, 1].tick_params(axis='x', rotation=45)

        plt.tight_layout()

        # 保存图表
        chart_path = 'multi_strategy_performance.png'
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        print(f"📈 性能对比图表已保存到: {chart_path}")

        plt.close()

    except Exception as e:
        print(f"创建图表失败: {e}")
        logger.error(f"创建图表失败: {e}")


def main():
    """主函数"""
    print("币安量化交易系统 - 全策略测试")
    print("=" * 60)
    print("请选择测试模式:")
    print("1. 快速功能测试 (测试所有策略基本功能)")
    print("2. 综合回测分析 (多策略、多交易对、多时间段回测)")
    print("3. 策略性能对比 (单一交易对策略对比)")
    print("4. 退出")

    while True:
        try:
            choice = input("\n请输入选择 (1-4): ").strip()

            if choice == '1':
                # 快速功能测试
                tests = [
                    ("DOJIplus趋势策略", test_doji_plus_strategy),
                    ("三角套利策略", test_triangle_arbitrage_strategy),
                    ("网格交易策略", test_matrix_grid_strategy),
                    ("实时信号生成", test_real_time_signals),
                ]

                results = []

                for test_name, test_func in tests:
                    try:
                        print(f"\n开始测试: {test_name}")
                        result = test_func()
                        results.append((test_name, result))
                        print(f"{'✓ 通过' if result else '✗ 失败'}")
                    except Exception as e:
                        print(f"✗ {test_name} 测试异常: {e}")
                        results.append((test_name, False))

                # 显示测试汇总
                print("\n" + "=" * 60)
                print("测试结果汇总")
                print("=" * 60)

                passed = 0
                total = len(results)

                for test_name, result in results:
                    status = "✓ 通过" if result else "✗ 失败"
                    print(f"{test_name}: {status}")
                    if result:
                        passed += 1

                print(f"\n总计: {passed}/{total} 个测试通过")

                if passed == total:
                    print("🎉 所有策略测试通过！系统可以投入使用。")
                else:
                    print("⚠️  部分测试失败，请检查相关配置。")
                break

            elif choice == '2':
                # 综合回测分析
                print("\n启动综合回测分析...")
                print("⚠️  注意: 这将需要较长时间，请耐心等待...")

                confirm = input("确认开始综合回测? (y/N): ").strip().lower()
                if confirm == 'y':
                    comprehensive_backtest()
                else:
                    print("已取消综合回测")
                break

            elif choice == '3':
                # 策略性能对比
                print("\n启动策略性能对比...")
                performance_comparison()
                break

            elif choice == '4':
                print("退出测试系统")
                break

            else:
                print("无效选择，请重新输入")

        except KeyboardInterrupt:
            print("\n用户中断")
            break
        except Exception as e:
            print(f"输入错误: {e}")


def run_quick_test():
    """快速测试函数，用于其他脚本调用"""
    tests = [
        ("DOJIplus趋势策略", test_doji_plus_strategy),
        ("三角套利策略", test_triangle_arbitrage_strategy),
        ("网格交易策略", test_matrix_grid_strategy),
        ("实时信号生成", test_real_time_signals),
        ("策略性能对比", performance_comparison),
    ]

    results = []

    for test_name, test_func in tests:
        try:
            print(f"\n开始测试: {test_name}")
            result = test_func()
            results.append((test_name, result))
            print(f"{'✓ 通过' if result else '✗ 失败'}")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
            results.append((test_name, False))

    return results


if __name__ == "__main__":
    main()
