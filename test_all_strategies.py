#!/usr/bin/env python3
"""
全策略测试脚本
测试所有三种策略的功能和表现
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.api.binance_client import BinanceClient
from src.data.data_fetcher import DataFetcher
from src.strategies.doji_plus import DojiPlusStrategy
from src.strategies.triangle_arbitrage import TriangleArbitrageStrategy
from src.strategies.matrix_grid import MatrixGridStrategy
from src.backtest.backtester import Backtester
from config.config import config
from src.utils.logger import get_logger

logger = get_logger(__name__)


def test_doji_plus_strategy():
    """测试DOJIplus趋势策略"""
    print("=" * 60)
    print("测试 DOJIplus 趋势策略")
    print("=" * 60)
    
    try:
        client = BinanceClient()
        data_fetcher = DataFetcher(client)
        
        # 创建策略
        strategy_config = config.get_strategy_config('doji_plus')
        strategy = DojiPlusStrategy(client, data_fetcher, strategy_config)
        
        print(f"✓ 策略创建成功")
        print(f"  配置: {strategy_config}")
        
        # 获取测试数据
        data = data_fetcher.get_historical_data('BTCUSDT', '1h', days=30)
        print(f"✓ 获取历史数据: {len(data)} 条")
        
        # 生成信号
        signals = strategy.generate_signals('BTCUSDT', data)
        print(f"✓ 信号生成: {len(signals)} 个信号")
        
        # 获取策略状态
        status = strategy.get_strategy_status('BTCUSDT')
        print(f"✓ 策略状态获取成功")
        print(f"  当前价格: {status['current_price']:.2f}")
        print(f"  RSI: {status['indicators']['rsi']:.2f}")
        print(f"  MACD: {status['indicators']['macd']:.6f}")
        
        # 简单回测
        backtester = Backtester(data_fetcher)
        result = backtester.run_backtest(strategy, 'BTCUSDT', '2024-01-01', '2024-12-31', '1h')
        
        print(f"✓ 回测完成")
        print(f"  总收益: {result.total_return:.2f} USDT ({result.total_return_pct:.2%})")
        print(f"  交易次数: {result.total_trades}")
        print(f"  胜率: {result.win_rate:.2%}")
        print(f"  最大回撤: {result.max_drawdown:.2%}")
        
        return True
        
    except Exception as e:
        print(f"✗ DOJIplus策略测试失败: {e}")
        return False


def test_triangle_arbitrage_strategy():
    """测试三角套利策略"""
    print("=" * 60)
    print("测试 Triangle Theme 三角套利策略")
    print("=" * 60)
    
    try:
        client = BinanceClient()
        data_fetcher = DataFetcher(client)
        
        # 创建策略
        strategy_config = config.get_strategy_config('triangle_arbitrage')
        strategy = TriangleArbitrageStrategy(client, data_fetcher, strategy_config)
        
        print(f"✓ 策略创建成功")
        print(f"  监控三角组合: {strategy.triangles}")
        print(f"  最小利润阈值: {strategy.min_profit_threshold:.3f}")
        
        # 获取测试数据
        data = data_fetcher.get_historical_data('BTCUSDT', '1h', days=7)
        print(f"✓ 获取历史数据: {len(data)} 条")
        
        # 生成信号 (三角套利基于实时价格，不依赖历史数据)
        signals = strategy.generate_signals('BTCUSDT', data)
        print(f"✓ 信号生成: {len(signals)} 个套利信号")
        
        # 获取套利摘要
        summary = strategy.get_arbitrage_summary()
        print(f"✓ 套利摘要:")
        print(f"  监控三角组合: {summary['triangles_monitored']} 个")
        print(f"  套利机会: {summary['total_opportunities']} 个")
        print(f"  平均利润率: {summary['avg_profit_rate']:.4f}")
        
        return True
        
    except Exception as e:
        print(f"✗ 三角套利策略测试失败: {e}")
        return False


def test_matrix_grid_strategy():
    """测试网格交易策略"""
    print("=" * 60)
    print("测试 MatrixHFGT 网格交易策略")
    print("=" * 60)
    
    try:
        client = BinanceClient()
        data_fetcher = DataFetcher(client)
        
        # 创建策略
        strategy_config = config.get_strategy_config('grid_trading')
        strategy = MatrixGridStrategy(client, data_fetcher, strategy_config)
        
        print(f"✓ 策略创建成功")
        print(f"  网格数量: {strategy.grid_count}")
        print(f"  价格范围: {strategy.price_range_pct:.1%}")
        print(f"  每单金额比例: {strategy.order_amount_pct:.1%}")
        
        # 获取测试数据
        data = data_fetcher.get_historical_data('BTCUSDT', '1h', days=7)
        print(f"✓ 获取历史数据: {len(data)} 条")
        
        # 生成信号
        signals = strategy.generate_signals('BTCUSDT', data)
        print(f"✓ 信号生成: {len(signals)} 个网格信号")
        
        # 获取网格状态
        if 'BTCUSDT' in strategy.grids:
            status = strategy.get_grid_status('BTCUSDT')
            print(f"✓ 网格状态:")
            print(f"  中心价格: {status['grid_info']['center_price']:.2f}")
            print(f"  价格范围: {status['grid_info']['lower_price']:.2f} - {status['grid_info']['upper_price']:.2f}")
            print(f"  网格间距: {status['grid_info']['grid_spacing']:.2f}")
            print(f"  活跃订单: {status['active_orders']} 个")
        
        return True
        
    except Exception as e:
        print(f"✗ 网格交易策略测试失败: {e}")
        return False


def test_real_time_signals():
    """测试实时信号生成"""
    print("=" * 60)
    print("测试实时信号生成")
    print("=" * 60)
    
    try:
        client = BinanceClient()
        data_fetcher = DataFetcher(client)
        
        # 创建所有策略
        strategies = {}
        
        # DOJIplus策略
        doji_config = config.get_strategy_config('doji_plus')
        strategies['DOJIplus'] = DojiPlusStrategy(client, data_fetcher, doji_config)
        
        # 三角套利策略
        triangle_config = config.get_strategy_config('triangle_arbitrage')
        strategies['TriangleArbitrage'] = TriangleArbitrageStrategy(client, data_fetcher, triangle_config)
        
        # 网格交易策略
        grid_config = config.get_strategy_config('grid_trading')
        strategies['MatrixGrid'] = MatrixGridStrategy(client, data_fetcher, grid_config)
        
        print(f"✓ 创建了 {len(strategies)} 个策略")
        
        # 获取实时数据
        symbols = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT']
        
        for symbol in symbols:
            print(f"\n--- {symbol} 信号测试 ---")
            
            # 获取历史数据
            data = data_fetcher.get_historical_data(symbol, '1h', days=30)
            current_price = data_fetcher.get_realtime_price(symbol)
            
            print(f"当前价格: {current_price:.2f}")
            
            # 测试每个策略
            for name, strategy in strategies.items():
                try:
                    signals = strategy.generate_signals(symbol, data)
                    print(f"  {name}: {len(signals)} 个信号")
                    
                    for signal in signals:
                        print(f"    - {signal.signal_type.value} @ {signal.price:.2f} "
                              f"(置信度: {signal.confidence:.2f}) - {signal.reason}")
                        
                except Exception as e:
                    print(f"  {name}: 信号生成失败 - {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ 实时信号测试失败: {e}")
        return False


def performance_comparison():
    """策略性能对比"""
    print("=" * 60)
    print("策略性能对比分析")
    print("=" * 60)
    
    try:
        client = BinanceClient()
        data_fetcher = DataFetcher(client)
        backtester = Backtester(data_fetcher)
        
        strategies = []
        results = []
        
        # DOJIplus策略
        doji_config = config.get_strategy_config('doji_plus')
        doji_strategy = DojiPlusStrategy(client, data_fetcher, doji_config)
        strategies.append(('DOJIplus', doji_strategy))
        
        print("开始策略对比回测...")
        
        # 运行回测
        for name, strategy in strategies:
            print(f"\n正在回测: {name}")
            try:
                result = backtester.run_backtest(
                    strategy=strategy,
                    symbol='BTCUSDT',
                    start_date='2024-01-01',
                    end_date='2024-12-31',
                    timeframe='1h'
                )
                results.append((name, result))
                print(f"✓ {name} 回测完成")
            except Exception as e:
                print(f"✗ {name} 回测失败: {e}")
        
        # 显示对比结果
        if results:
            print("\n" + "=" * 100)
            print("策略性能对比结果")
            print("=" * 100)
            print(f"{'策略':<15} {'初始资金':<12} {'最终资金':<12} {'总收益':<12} {'收益率':<10} "
                  f"{'交易次数':<8} {'胜率':<8} {'最大回撤':<10} {'夏普比率':<10}")
            print("-" * 100)
            
            for name, result in results:
                print(f"{name:<15} "
                      f"{result.initial_balance:<12.2f} "
                      f"{result.final_balance:<12.2f} "
                      f"{result.total_return:<12.2f} "
                      f"{result.total_return_pct:<10.2%} "
                      f"{result.total_trades:<8} "
                      f"{result.win_rate:<8.2%} "
                      f"{result.max_drawdown:<10.2%} "
                      f"{result.sharpe_ratio:<10.2f}")
        
        return True
        
    except Exception as e:
        print(f"✗ 策略性能对比失败: {e}")
        return False


def main():
    """主函数"""
    print("币安量化交易系统 - 全策略测试")
    print("=" * 60)
    
    tests = [
        ("DOJIplus趋势策略", test_doji_plus_strategy),
        ("三角套利策略", test_triangle_arbitrage_strategy),
        ("网格交易策略", test_matrix_grid_strategy),
        ("实时信号生成", test_real_time_signals),
        ("策略性能对比", performance_comparison),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            print(f"\n开始测试: {test_name}")
            result = test_func()
            results.append((test_name, result))
            print(f"{'✓ 通过' if result else '✗ 失败'}")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试汇总
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有策略测试通过！系统可以投入使用。")
    else:
        print("⚠️  部分测试失败，请检查相关配置。")


if __name__ == "__main__":
    main()
