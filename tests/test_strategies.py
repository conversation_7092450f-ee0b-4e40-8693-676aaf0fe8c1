#!/usr/bin/env python3
"""
策略测试模块
测试所有三种策略的基本功能
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch
import pandas as pd
import numpy as np

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.api.binance_client import BinanceClient
from src.data.data_fetcher import DataFetcher
from src.strategies.doji_plus import DojiPlusStrategy
from src.strategies.triangle_arbitrage import TriangleArbitrageStrategy
from src.strategies.matrix_grid import MatrixGridStrategy
from config.config import config


class TestStrategies(unittest.TestCase):
    """策略测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建模拟的客户端和数据获取器
        self.mock_client = Mock(spec=BinanceClient)
        self.mock_data_fetcher = Mock(spec=DataFetcher)
        
        # 创建测试数据
        self.test_data = self._create_test_data()
        
        # 配置模拟返回值
        self.mock_data_fetcher.get_historical_data.return_value = self.test_data
        self.mock_data_fetcher.get_realtime_price.return_value = 50000.0
        
        # 模拟账户信息
        self.mock_client.get_account_info.return_value = {
            'balances': [
                {'asset': 'USDT', 'free': '10000.0'},
                {'asset': 'BTC', 'free': '0.1'},
                {'asset': 'ETH', 'free': '1.0'},
                {'asset': 'SOL', 'free': '10.0'}
            ]
        }
    
    def _create_test_data(self) -> pd.DataFrame:
        """创建测试数据"""
        dates = pd.date_range('2024-01-01', periods=100, freq='1H')
        np.random.seed(42)  # 固定随机种子
        
        # 生成模拟价格数据
        base_price = 50000
        returns = np.random.normal(0, 0.02, 100)
        prices = [base_price]
        
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        data = pd.DataFrame({
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
            'close': prices,
            'volume': np.random.uniform(1000, 10000, 100)
        }, index=dates)
        
        return data
    
    def test_doji_plus_strategy(self):
        """测试DOJIplus策略"""
        strategy_config = config.get_strategy_config('doji_plus')
        strategy = DojiPlusStrategy(self.mock_client, self.mock_data_fetcher, strategy_config)
        
        # 测试信号生成
        signals = strategy.generate_signals('BTCUSDT', self.test_data)
        
        # 验证结果
        self.assertIsInstance(signals, list)
        print(f"DOJIplus策略生成了 {len(signals)} 个信号")
        
        # 测试策略状态
        with patch.object(strategy, '_calculate_indicators') as mock_calc:
            mock_calc.return_value = self.test_data.copy()
            mock_calc.return_value['rsi'] = 50.0
            mock_calc.return_value['macd'] = 0.001
            
            status = strategy.get_strategy_status('BTCUSDT')
            self.assertIn('current_price', status)
            self.assertIn('indicators', status)
    
    def test_triangle_arbitrage_strategy(self):
        """测试三角套利策略"""
        strategy_config = config.get_strategy_config('triangle_arbitrage')
        strategy = TriangleArbitrageStrategy(self.mock_client, self.mock_data_fetcher, strategy_config)
        
        # 测试信号生成
        signals = strategy.generate_signals('BTCUSDT', self.test_data)
        
        # 验证结果
        self.assertIsInstance(signals, list)
        print(f"三角套利策略生成了 {len(signals)} 个信号")
        
        # 测试套利摘要
        summary = strategy.get_arbitrage_summary()
        self.assertIn('triangles_monitored', summary)
        self.assertIn('total_opportunities', summary)
    
    def test_matrix_grid_strategy(self):
        """测试网格交易策略"""
        strategy_config = config.get_strategy_config('grid_trading')
        strategy = MatrixGridStrategy(self.mock_client, self.mock_data_fetcher, strategy_config)
        
        # 测试信号生成
        signals = strategy.generate_signals('BTCUSDT', self.test_data)
        
        # 验证结果
        self.assertIsInstance(signals, list)
        print(f"网格交易策略生成了 {len(signals)} 个信号")
        
        # 测试网格状态
        if 'BTCUSDT' in strategy.grids:
            status = strategy.get_grid_status('BTCUSDT')
            self.assertIn('grid_info', status)
            self.assertIn('active_orders', status)


if __name__ == '__main__':
    unittest.main()
