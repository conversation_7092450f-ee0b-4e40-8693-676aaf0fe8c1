#!/usr/bin/env python3
"""
测试运行器
运行所有测试用例
"""

import sys
import os
import unittest

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def run_unit_tests():
    """运行单元测试"""
    print("=" * 60)
    print("运行单元测试")
    print("=" * 60)
    
    # 发现并运行测试
    loader = unittest.TestLoader()
    start_dir = os.path.dirname(__file__)
    suite = loader.discover(start_dir, pattern='test_*.py')
    
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()


def run_integration_tests():
    """运行集成测试"""
    print("=" * 60)
    print("运行集成测试")
    print("=" * 60)
    
    try:
        from test_backtest_comprehensive import (
            test_doji_plus_strategy,
            test_triangle_arbitrage_strategy,
            test_matrix_grid_strategy,
            test_real_time_signals
        )
        
        tests = [
            ("DOJIplus策略测试", test_doji_plus_strategy),
            ("三角套利策略测试", test_triangle_arbitrage_strategy),
            ("网格交易策略测试", test_matrix_grid_strategy),
            ("实时信号测试", test_real_time_signals),
        ]
        
        results = []
        
        for test_name, test_func in tests:
            try:
                print(f"\n开始测试: {test_name}")
                result = test_func()
                results.append((test_name, result))
                print(f"{'✓ 通过' if result else '✗ 失败'}")
            except Exception as e:
                print(f"✗ {test_name} 测试异常: {e}")
                results.append((test_name, False))
        
        # 显示测试汇总
        print("\n" + "=" * 60)
        print("集成测试结果汇总")
        print("=" * 60)
        
        passed = 0
        total = len(results)
        
        for test_name, result in results:
            status = "✓ 通过" if result else "✗ 失败"
            print(f"{test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n总计: {passed}/{total} 个测试通过")
        
        return passed == total
        
    except Exception as e:
        print(f"集成测试失败: {e}")
        return False


def main():
    """主函数"""
    print("币安量化交易系统 - 测试套件")
    print("=" * 60)
    print("请选择测试类型:")
    print("1. 单元测试 (快速)")
    print("2. 集成测试 (需要网络连接)")
    print("3. 全部测试")
    print("4. 退出")
    
    while True:
        try:
            choice = input("\n请输入选择 (1-4): ").strip()
            
            if choice == '1':
                success = run_unit_tests()
                print(f"\n单元测试 {'通过' if success else '失败'}")
                break
                
            elif choice == '2':
                success = run_integration_tests()
                print(f"\n集成测试 {'通过' if success else '失败'}")
                break
                
            elif choice == '3':
                print("运行全部测试...")
                unit_success = run_unit_tests()
                integration_success = run_integration_tests()
                
                overall_success = unit_success and integration_success
                print(f"\n全部测试 {'通过' if overall_success else '失败'}")
                print(f"  单元测试: {'通过' if unit_success else '失败'}")
                print(f"  集成测试: {'通过' if integration_success else '失败'}")
                break
                
            elif choice == '4':
                print("退出测试")
                break
                
            else:
                print("无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n用户中断")
            break
        except Exception as e:
            print(f"输入错误: {e}")


if __name__ == "__main__":
    main()
