#!/usr/bin/env python3
"""
使用代理的系统测试脚本
解决网络连接和无效交易对问题
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置代理环境变量
os.environ['HTTP_PROXY'] = 'http://127.0.0.1:7890'
os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:7890'
os.environ['ALL_PROXY'] = 'http://127.0.0.1:7890'

from config.config import config
from src.utils.logger import get_logger
from src.api.binance_client import BinanceClient
from src.data.data_fetcher import DataFetcher
from src.data.indicators import TechnicalIndicators
from src.strategies.doji_plus import DojiPlusStrategy

logger = get_logger(__name__)


def test_network_connection():
    """测试网络连接"""
    print("=" * 50)
    print("测试网络连接")
    print("=" * 50)
    
    try:
        import requests
        
        # 测试币安API连接
        response = requests.get('https://api.binance.com/api/v3/ping', timeout=10)
        print(f"✓ 币安API连接成功，状态码: {response.status_code}")
        
        # 测试获取服务器时间
        response = requests.get('https://api.binance.com/api/v3/time', timeout=10)
        if response.status_code == 200:
            server_time = response.json()
            print(f"✓ 服务器时间获取成功: {server_time['serverTime']}")
        
        return True
        
    except Exception as e:
        print(f"✗ 网络连接失败: {e}")
        return False


def test_valid_symbols():
    """测试有效交易对"""
    print("=" * 50)
    print("测试有效交易对")
    print("=" * 50)
    
    try:
        client = BinanceClient()
        
        # 获取交易所信息
        exchange_info = client.client.get_exchange_info()
        
        # 筛选我们关心的USDT交易对
        target_assets = ['BTC', 'ETH', 'SOL', 'ADA', 'DOT', 'LINK', 'UNI']
        valid_symbols = []
        
        for symbol_info in exchange_info['symbols']:
            if (symbol_info['quoteAsset'] == 'USDT' and 
                symbol_info['status'] == 'TRADING' and
                symbol_info['baseAsset'] in target_assets):
                valid_symbols.append(symbol_info['symbol'])
        
        print(f"✓ 找到 {len(valid_symbols)} 个有效的USDT交易对:")
        for symbol in sorted(valid_symbols):
            try:
                price = client.get_ticker_price(symbol)
                print(f"  {symbol}: {price:.2f} USDT")
            except Exception as e:
                print(f"  {symbol}: 获取价格失败 - {e}")
        
        # 更新配置中的交易对
        config.TRADING_PAIRS = valid_symbols[:3]  # 只使用前3个
        print(f"\n✓ 更新交易对配置: {config.TRADING_PAIRS}")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试有效交易对失败: {e}")
        return False


def test_binance_client_fixed():
    """测试修复后的币安客户端"""
    print("=" * 50)
    print("测试修复后的币安客户端")
    print("=" * 50)
    
    try:
        client = BinanceClient()
        print("✓ 客户端创建成功")
        
        # 测试连接
        if client.test_connection():
            print("✓ API连接测试成功")
        else:
            print("✗ API连接测试失败")
            return False
        
        # 获取账户信息（修复后的版本）
        account_info = client.get_account_info()
        print(f"✓ 账户信息获取成功")
        print(f"  - 账户类型: {account_info['account_type']}")
        print(f"  - 可交易: {account_info['can_trade']}")
        print(f"  - 总余额: {account_info['total_balance_usdt']:.2f} USDT")
        print(f"  - 非零余额资产: {len(account_info['balances'])} 个")
        
        # 显示主要资产余额
        print("  - 主要资产余额:")
        for balance in account_info['balances'][:5]:  # 显示前5个
            print(f"    {balance['asset']}: {balance['total']:.6f}")
        
        return True
        
    except Exception as e:
        print(f"✗ 币安客户端测试失败: {e}")
        return False


def test_strategy_with_valid_symbols():
    """使用有效交易对测试策略"""
    print("=" * 50)
    print("测试策略（使用有效交易对）")
    print("=" * 50)
    
    try:
        client = BinanceClient()
        data_fetcher = DataFetcher(client)
        
        # 使用有效的交易对
        test_symbols = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT']
        
        for symbol in test_symbols:
            print(f"\n--- 测试 {symbol} ---")
            
            try:
                # 获取实时价格
                price = data_fetcher.get_realtime_price(symbol)
                print(f"✓ 实时价格: {price:.2f}")
                
                # 获取历史数据
                data = data_fetcher.get_historical_data(symbol, '1h', days=7)
                print(f"✓ 历史数据: {len(data)} 条")
                
                # 计算技术指标
                indicators = TechnicalIndicators.calculate_all_indicators(data)
                latest = indicators.iloc[-1]
                print(f"✓ 技术指标:")
                print(f"  RSI: {latest['rsi']:.2f}")
                print(f"  MACD: {latest['macd']:.6f}")
                print(f"  SMA(20): {latest['sma_20']:.2f}")
                
                # 测试策略
                doji_config = config.get_strategy_config('doji_plus')
                strategy = DojiPlusStrategy(client, data_fetcher, doji_config)
                
                signals = strategy.generate_signals(symbol, data)
                print(f"✓ 策略信号: {len(signals)} 个")
                
                for signal in signals:
                    print(f"  - {signal.signal_type.value} @ {signal.price:.2f} "
                          f"(置信度: {signal.confidence:.2f})")
                
            except Exception as e:
                print(f"✗ {symbol} 测试失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ 策略测试失败: {e}")
        return False


def test_triangle_arbitrage_opportunities():
    """测试三角套利机会"""
    print("=" * 50)
    print("测试三角套利机会")
    print("=" * 50)
    
    try:
        from src.strategies.triangle_arbitrage import TriangleArbitrageStrategy
        
        client = BinanceClient()
        data_fetcher = DataFetcher(client)
        
        # 创建三角套利策略
        triangle_config = config.get_strategy_config('triangle_arbitrage')
        strategy = TriangleArbitrageStrategy(client, data_fetcher, triangle_config)
        
        print(f"✓ 三角套利策略创建成功")
        print(f"监控的三角组合: {strategy.triangles}")
        
        # 获取当前价格
        symbols = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT']
        prices = {}
        
        for symbol in symbols:
            try:
                price = data_fetcher.get_realtime_price(symbol)
                prices[symbol] = price
                print(f"✓ {symbol}: {price:.2f}")
            except Exception as e:
                print(f"✗ {symbol}: {e}")
        
        # 检查套利机会
        print("\n检查套利机会...")
        for triangle in strategy.triangles:
            opportunities = strategy._find_arbitrage_opportunities(triangle)
            if opportunities:
                for opp in opportunities:
                    print(f"✓ 发现套利机会: {opp['path']}")
                    print(f"  利润率: {opp['net_profit_rate']:.4f}")
                    print(f"  预期利润: {opp['net_profit']:.2f} USDT")
            else:
                print(f"- {triangle}: 暂无套利机会")
        
        return True
        
    except Exception as e:
        print(f"✗ 三角套利测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("币安量化交易系统 - 代理网络测试")
    print("使用代理: http://127.0.0.1:7890")
    print()
    
    tests = [
        ("网络连接", test_network_connection),
        ("有效交易对", test_valid_symbols),
        ("币安客户端", test_binance_client_fixed),
        ("策略测试", test_strategy_with_valid_symbols),
        ("三角套利", test_triangle_arbitrage_opportunities),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print()
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
            results.append((test_name, False))
            print()
    
    # 显示测试结果汇总
    print("=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print()
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统可以正常运行。")
        print("\n建议:")
        print("1. 现在可以运行 python main.py 启动实盘交易")
        print("2. 或运行 python run_backtest.py 进行策略回测")
        print("3. 记得在实盘交易前先在测试网络中验证")
        return True
    else:
        print("⚠️  部分测试失败，请检查配置和网络连接。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
