#!/usr/bin/env python3
"""
综合回测测试模块
包含多策略回测分析和性能对比功能
"""

import sys
import os
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import seaborn as sns
from matplotlib.patches import Rectangle

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.api.binance_client import BinanceClient
from src.data.data_fetcher import DataFetcher
from src.strategies.doji_plus import DojiPlusStrategy
from src.strategies.triangle_arbitrage import TriangleArbitrageStrategy
from src.strategies.matrix_grid import MatrixGridStrategy
from src.backtest.backtester import Backtester, BacktestResult
from config.config import config
from src.utils.logger import get_logger

logger = get_logger(__name__)

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")


def test_doji_plus_strategy():
    """测试DOJIplus趋势策略"""
    print("=" * 60)
    print("测试 DOJIplus 趋势策略")
    print("=" * 60)
    
    try:
        client = BinanceClient()
        data_fetcher = DataFetcher(client)
        
        # 创建策略
        strategy_config = config.get_strategy_config('doji_plus')
        strategy = DojiPlusStrategy(client, data_fetcher, strategy_config)
        
        print(f"✓ 策略创建成功")
        print(f"  配置: {strategy_config}")
        
        # 获取测试数据
        data = data_fetcher.get_historical_data('BTCUSDT', '1h', days=30)
        print(f"✓ 获取历史数据: {len(data)} 条")
        
        # 生成信号
        signals = strategy.generate_signals('BTCUSDT', data)
        print(f"✓ 信号生成: {len(signals)} 个信号")
        
        # 获取策略状态
        status = strategy.get_strategy_status('BTCUSDT')
        print(f"✓ 策略状态获取成功")
        print(f"  当前价格: {status['current_price']:.2f}")
        print(f"  RSI: {status['indicators']['rsi']:.2f}")
        print(f"  MACD: {status['indicators']['macd']:.6f}")
        
        # 简单回测
        backtester = Backtester(data_fetcher)
        result = backtester.run_backtest(strategy, 'BTCUSDT', '2024-01-01', '2024-12-31', '1h')
        
        print(f"✓ 回测完成")
        print(f"  总收益: {result.total_return:.2f} USDT ({result.total_return_pct:.2%})")
        print(f"  交易次数: {result.total_trades}")
        print(f"  胜率: {result.win_rate:.2%}")
        print(f"  最大回撤: {result.max_drawdown:.2%}")
        
        return True
        
    except Exception as e:
        print(f"✗ DOJIplus策略测试失败: {e}")
        return False


def test_triangle_arbitrage_strategy():
    """测试三角套利策略"""
    print("=" * 60)
    print("测试 Triangle Theme 三角套利策略")
    print("=" * 60)
    
    try:
        client = BinanceClient()
        data_fetcher = DataFetcher(client)
        
        # 创建策略
        strategy_config = config.get_strategy_config('triangle_arbitrage')
        strategy = TriangleArbitrageStrategy(client, data_fetcher, strategy_config)
        
        print(f"✓ 策略创建成功")
        print(f"  监控三角组合: {strategy.triangles}")
        print(f"  最小利润阈值: {strategy.min_profit_threshold:.3f}")
        
        # 获取测试数据
        data = data_fetcher.get_historical_data('BTCUSDT', '1h', days=7)
        print(f"✓ 获取历史数据: {len(data)} 条")
        
        # 生成信号 (三角套利基于实时价格，不依赖历史数据)
        signals = strategy.generate_signals('BTCUSDT', data)
        print(f"✓ 信号生成: {len(signals)} 个套利信号")
        
        # 获取套利摘要
        summary = strategy.get_arbitrage_summary()
        print(f"✓ 套利摘要:")
        print(f"  监控三角组合: {summary['triangles_monitored']} 个")
        print(f"  套利机会: {summary['total_opportunities']} 个")
        print(f"  平均利润率: {summary['avg_profit_rate']:.4f}")
        
        return True
        
    except Exception as e:
        print(f"✗ 三角套利策略测试失败: {e}")
        return False


def test_matrix_grid_strategy():
    """测试网格交易策略"""
    print("=" * 60)
    print("测试 MatrixHFGT 网格交易策略")
    print("=" * 60)
    
    try:
        client = BinanceClient()
        data_fetcher = DataFetcher(client)
        
        # 创建策略
        strategy_config = config.get_strategy_config('grid_trading')
        strategy = MatrixGridStrategy(client, data_fetcher, strategy_config)
        
        print(f"✓ 策略创建成功")
        print(f"  网格数量: {strategy.grid_count}")
        print(f"  价格范围: {strategy.price_range_pct:.1%}")
        print(f"  每单金额比例: {strategy.order_amount_pct:.1%}")
        
        # 获取测试数据
        data = data_fetcher.get_historical_data('BTCUSDT', '1h', days=7)
        print(f"✓ 获取历史数据: {len(data)} 条")
        
        # 生成信号
        signals = strategy.generate_signals('BTCUSDT', data)
        print(f"✓ 信号生成: {len(signals)} 个网格信号")
        
        # 获取网格状态
        if 'BTCUSDT' in strategy.grids:
            status = strategy.get_grid_status('BTCUSDT')
            print(f"✓ 网格状态:")
            print(f"  中心价格: {status['grid_info']['center_price']:.2f}")
            print(f"  价格范围: {status['grid_info']['lower_price']:.2f} - {status['grid_info']['upper_price']:.2f}")
            print(f"  网格间距: {status['grid_info']['grid_spacing']:.2f}")
            print(f"  活跃订单: {status['active_orders']} 个")
        
        return True
        
    except Exception as e:
        print(f"✗ 网格交易策略测试失败: {e}")
        return False


def test_real_time_signals():
    """测试实时信号生成"""
    print("=" * 60)
    print("测试实时信号生成")
    print("=" * 60)
    
    try:
        client = BinanceClient()
        data_fetcher = DataFetcher(client)
        
        # 创建所有策略
        strategies = {}
        
        # DOJIplus策略
        doji_config = config.get_strategy_config('doji_plus')
        strategies['DOJIplus'] = DojiPlusStrategy(client, data_fetcher, doji_config)
        
        # 三角套利策略
        triangle_config = config.get_strategy_config('triangle_arbitrage')
        strategies['TriangleArbitrage'] = TriangleArbitrageStrategy(client, data_fetcher, triangle_config)
        
        # 网格交易策略
        grid_config = config.get_strategy_config('grid_trading')
        strategies['MatrixGrid'] = MatrixGridStrategy(client, data_fetcher, grid_config)
        
        print(f"✓ 创建了 {len(strategies)} 个策略")
        
        # 获取实时数据
        symbols = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT']
        
        for symbol in symbols:
            print(f"\n--- {symbol} 信号测试 ---")
            
            # 获取历史数据
            data = data_fetcher.get_historical_data(symbol, '1h', days=30)
            current_price = data_fetcher.get_realtime_price(symbol)
            
            print(f"当前价格: {current_price:.2f}")
            
            # 测试每个策略
            for name, strategy in strategies.items():
                try:
                    signals = strategy.generate_signals(symbol, data)
                    print(f"  {name}: {len(signals)} 个信号")
                    
                    for signal in signals:
                        print(f"    - {signal.signal_type.value} @ {signal.price:.2f} "
                              f"(置信度: {signal.confidence:.2f}) - {signal.reason}")
                        
                except Exception as e:
                    print(f"  {name}: 信号生成失败 - {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ 实时信号测试失败: {e}")
        return False


if __name__ == "__main__":
    # 这里只包含基本测试函数，完整的回测功能在原文件中
    print("基本策略测试模块")
    print("如需完整的回测功能，请运行项目根目录下的 test_all_strategies.py")
