#!/usr/bin/env python3
"""
快速测试脚本 - 验证修复后的系统
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置代理
os.environ['HTTP_PROXY'] = 'http://127.0.0.1:7890'
os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:7890'
os.environ['ALL_PROXY'] = 'http://127.0.0.1:7890'

from src.api.binance_client import BinanceClient
from src.data.data_fetcher import DataFetcher
from src.strategies.doji_plus import DojiPlusStrategy
from config.config import config


def test_basic_functions():
    """测试基本功能"""
    print("🚀 快速测试 - 验证修复后的系统")
    print("=" * 50)
    
    try:
        # 1. 测试API连接
        print("1. 测试API连接...")
        client = BinanceClient()
        if client.test_connection():
            print("   ✓ API连接成功")
        else:
            print("   ✗ API连接失败")
            return False
        
        # 2. 测试简化的账户信息（只获取主要资产）
        print("2. 测试账户信息...")
        try:
            account = client.client.get_account()
            print("   ✓ 原始账户信息获取成功")
            
            # 手动计算主要资产余额
            main_assets = ['USDT', 'BTC', 'ETH', 'SOL', 'BNB']
            total_value = 0.0
            
            for balance in account['balances']:
                asset = balance['asset']
                amount = float(balance['free']) + float(balance['locked'])
                
                if amount > 0 and asset in main_assets:
                    if asset == 'USDT':
                        value = amount
                        print(f"   {asset}: {amount:.2f} USDT")
                    else:
                        try:
                            price = client.get_ticker_price(f"{asset}USDT")
                            value = amount * price
                            print(f"   {asset}: {amount:.6f} * {price:.2f} = {value:.2f} USDT")
                        except:
                            print(f"   {asset}: {amount:.6f} (价格获取失败)")
                            continue
                    
                    total_value += value
            
            print(f"   ✓ 主要资产总价值: {total_value:,.2f} USDT")
            
        except Exception as e:
            print(f"   ✗ 账户信息测试失败: {e}")
            return False
        
        # 3. 测试数据获取
        print("3. 测试数据获取...")
        data_fetcher = DataFetcher(client)
        
        try:
            price = data_fetcher.get_realtime_price('BTCUSDT')
            print(f"   ✓ BTC价格: {price:,.2f} USDT")
            
            data = data_fetcher.get_historical_data('BTCUSDT', '1h', days=7)
            print(f"   ✓ 历史数据: {len(data)} 条K线")
            
        except Exception as e:
            print(f"   ✗ 数据获取失败: {e}")
            return False
        
        # 4. 测试策略
        print("4. 测试策略...")
        try:
            strategy_config = config.get_strategy_config('doji_plus')
            strategy = DojiPlusStrategy(client, data_fetcher, strategy_config)
            print("   ✓ DOJIplus策略创建成功")
            
            signals = strategy.generate_signals('BTCUSDT', data)
            print(f"   ✓ 信号生成: {len(signals)} 个信号")
            
            if signals:
                for signal in signals:
                    print(f"     - {signal.signal_type.value} @ {signal.price:.2f} "
                          f"(置信度: {signal.confidence:.2f})")
            
        except Exception as e:
            print(f"   ✗ 策略测试失败: {e}")
            return False
        
        print("\n🎉 所有测试通过！系统运行正常。")
        print("\n建议下一步:")
        print("- 运行 python main.py 启动完整系统")
        print("- 或运行 python run_backtest.py 进行回测")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False


def test_no_invalid_symbols():
    """专门测试是否还有invalid symbol错误"""
    print("\n" + "=" * 50)
    print("🔍 专项测试 - 验证无invalid symbol错误")
    print("=" * 50)
    
    try:
        client = BinanceClient()
        
        # 测试白名单方式的余额计算
        account = client.client.get_account()
        balances = []
        
        for balance in account['balances']:
            free = float(balance['free'])
            locked = float(balance['locked'])
            if free > 0 or locked > 0:
                balances.append({
                    'asset': balance['asset'],
                    'free': free,
                    'locked': locked,
                    'total': free + locked
                })
        
        print(f"账户中有 {len(balances)} 种非零余额资产")
        
        # 使用修复后的方法计算总余额
        total_balance = client._calculate_total_balance_usdt(balances)
        print(f"✓ 总余额计算成功: {total_balance:,.2f} USDT")
        print("✓ 无invalid symbol错误！")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False


if __name__ == "__main__":
    success1 = test_basic_functions()
    success2 = test_no_invalid_symbols()
    
    if success1 and success2:
        print("\n🎊 恭喜！系统已完全修复，可以正常使用了！")
        sys.exit(0)
    else:
        print("\n⚠️  仍有问题需要解决")
        sys.exit(1)
