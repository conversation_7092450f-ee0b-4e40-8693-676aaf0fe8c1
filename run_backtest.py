#!/usr/bin/env python3
"""
多策略回测系统
同时测试DOJIplus、三角套利和网格交易三种策略，并提供详细的性能对比分析
"""

import sys
import os
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle
import seaborn as sns

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.api.binance_client import BinanceClient
from src.data.data_fetcher import DataFetcher
from src.strategies.doji_plus import DojiPlusStrategy
from src.strategies.triangle_arbitrage import TriangleArbitrageStrategy
from src.strategies.matrix_grid import MatrixGridStrategy
from src.backtest.backtester import Backtester, BacktestResult
from config.config import config
from src.utils.logger import get_logger

logger = get_logger(__name__)

# 设置中文字体 - 使用阿里巴巴普惠体
import matplotlib.font_manager as fm

# 注册阿里巴巴普惠体字体
font_path = 'media/Alibaba-PuHuiTi-Regular.ttf'
try:
    fm.fontManager.addfont(font_path)
    plt.rcParams['font.family'] = ['Alibaba PuHuiTi', 'sans-serif']
    print("✓ 阿里巴巴普惠体字体加载成功")
except Exception as e:
    print(f"⚠️ 字体加载失败，使用默认字体: {e}")
    plt.rcParams['font.family'] = ['DejaVu Sans', 'sans-serif']

plt.rcParams['axes.unicode_minus'] = False


class MultiStrategyBacktester:
    """多策略回测系统"""

    def __init__(self):
        """初始化多策略回测器"""
        self.client = BinanceClient()
        self.data_fetcher = DataFetcher(self.client)
        # 使用1万USDC作为初始资金
        self.initial_balance = 10000.0
        self.backtester = Backtester(self.data_fetcher, initial_balance=self.initial_balance)

        # 策略配置
        self.strategies_config = {
            'DOJIplus': {
                'class': DojiPlusStrategy,
                'config': config.get_strategy_config('doji_plus'),
                'description': 'DOJIplus趋势策略 - 基于十字星形态和多技术指标'
            },
            'TriangleArbitrage': {
                'class': TriangleArbitrageStrategy,
                'config': config.get_strategy_config('triangle_arbitrage'),
                'description': '三角套利策略 - BTC/ETH/SOL三角套利'
            },
            'MatrixGrid': {
                'class': MatrixGridStrategy,
                'config': config.get_strategy_config('grid_trading'),
                'description': '网格交易策略 - 动态网格高频交易'
            }
        }

        # 回测参数
        self.backtest_params = {
            'symbols': ['BTCUSDT', 'ETHUSDT', 'SOLUSDT'],
            'timeframes': ['1h', '4h'],
            'periods': [
                ('短期', '2024-10-01', '2024-12-31'),
                ('中期', '2024-07-01', '2024-12-31'),
                ('长期', '2024-01-01', '2024-12-31')
            ]
        }

        logger.info("多策略回测系统初始化完成")

    def run_comprehensive_backtest(self) -> Dict[str, Any]:
        """运行综合回测"""
        print("=" * 80)
        print("多策略综合回测系统")
        print("=" * 80)

        all_results = {}

        for strategy_name, strategy_info in self.strategies_config.items():
            print(f"\n正在回测策略: {strategy_name}")
            print(f"描述: {strategy_info['description']}")
            print("-" * 60)

            strategy_results = {}

            # 创建策略实例
            try:
                strategy = strategy_info['class'](
                    self.client,
                    self.data_fetcher,
                    strategy_info['config']
                )

                # 对每个交易对和时间段进行回测
                for symbol in self.backtest_params['symbols']:
                    symbol_results = {}

                    for period_name, start_date, end_date in self.backtest_params['periods']:
                        print(f"  回测 {symbol} - {period_name} ({start_date} 到 {end_date})")

                        try:
                            # 选择合适的时间框架
                            timeframe = '1h' if strategy_name != 'TriangleArbitrage' else '5m'

                            result = self.backtester.run_backtest(
                                strategy=strategy,
                                symbol=symbol,
                                start_date=start_date,
                                end_date=end_date,
                                timeframe=timeframe
                            )

                            symbol_results[period_name] = result
                            print(f"    ✓ 完成 - 收益率: {result.total_return_pct:.2%}")

                        except Exception as e:
                            print(f"    ✗ 失败: {e}")
                            logger.error(f"回测失败 {strategy_name}-{symbol}-{period_name}: {e}")

                    strategy_results[symbol] = symbol_results

                all_results[strategy_name] = strategy_results
                print(f"✓ {strategy_name} 策略回测完成")

            except Exception as e:
                print(f"✗ {strategy_name} 策略初始化失败: {e}")
                logger.error(f"策略初始化失败 {strategy_name}: {e}")

        return all_results

    def run_90day_backtest(self) -> Dict[str, Any]:
        """运行90天回测 - 使用1万USDC本金"""
        print("=" * 80)
        print("90天多策略回测分析")
        print(f"初始资金: ${self.initial_balance:,.2f} USDC (每策略)")
        print("=" * 80)

        all_results = {}

        for strategy_name, strategy_info in self.strategies_config.items():
            print(f"\n{'='*60}")
            print(f"回测策略: {strategy_name}")
            print(f"描述: {strategy_info['description']}")
            print(f"{'='*60}")

            strategy_results = {}

            try:
                # 创建策略实例
                strategy = strategy_info['class'](
                    self.client,
                    self.data_fetcher,
                    strategy_info['config']
                )

                # 对每个交易对进行回测
                for symbol in ['BTCUSDT', 'ETHUSDT', 'SOLUSDT']:
                    print(f"\n--- 回测交易对: {symbol} ---")

                    try:
                        # 根据策略选择合适的时间框架
                        timeframe = '1h'
                        if strategy_name == 'TriangleArbitrage':
                            timeframe = '5m'  # 三角套利需要更高频率
                        elif strategy_name == 'MatrixGrid':
                            timeframe = '15m'  # 网格交易中等频率

                        # 获取90天数据
                        data = self.data_fetcher.get_historical_data(symbol, timeframe, days=90)

                        if data.empty:
                            print(f"    ✗ 无法获取 {symbol} 的历史数据")
                            continue

                        print(f"    数据范围: {data.index[0]} 到 {data.index[-1]}")
                        print(f"    数据条数: {len(data)}")

                        # 使用缓存数据进行回测
                        result = self.backtester.run_backtest_with_data(
                            strategy=strategy,
                            symbol=symbol,
                            data=data
                        )

                        strategy_results[symbol] = result

                        # 显示结果
                        print(f"    ✓ 回测完成")
                        print(f"      初始资金: ${result.initial_balance:,.2f} USDC")
                        print(f"      最终资金: ${result.final_balance:,.2f} USDC")
                        print(f"      总收益: ${result.total_return:,.2f} USDC ({result.total_return_pct:.2%})")
                        print(f"      交易次数: {result.total_trades}")
                        print(f"      胜率: {result.win_rate:.2%}")
                        print(f"      最大回撤: {result.max_drawdown:.2%}")
                        print(f"      夏普比率: {result.sharpe_ratio:.2f}")

                    except Exception as e:
                        print(f"    ✗ 回测失败: {e}")
                        logger.error(f"回测失败 {strategy_name}-{symbol}: {e}")

                all_results[strategy_name] = strategy_results
                print(f"\n✓ {strategy_name} 策略回测完成")

            except Exception as e:
                print(f"✗ {strategy_name} 策略初始化失败: {e}")
                logger.error(f"策略初始化失败 {strategy_name}: {e}")

        # 生成综合报告
        if all_results:
            self.generate_90day_report(all_results)

        return all_results

    def generate_90day_report(self, all_results: Dict[str, Any]):
        """生成90天回测报告"""
        print("\n" + "=" * 80)
        print("90天回测综合报告")
        print("=" * 80)

        # 策略汇总表
        print(f"\n📊 策略收益汇总 (初始资金: ${self.initial_balance:,.2f} USDC)")
        print("-" * 100)
        print(f"{'策略':<15} {'交易对':<10} {'最终资金':<12} {'总收益':<12} {'收益率':<10} "
              f"{'交易次数':<8} {'胜率':<8} {'最大回撤':<10} {'夏普比率':<10}")
        print("-" * 100)

        strategy_summary = {}

        for strategy_name, strategy_results in all_results.items():
            strategy_metrics = []

            for symbol, result in strategy_results.items():
                if result:
                    print(f"{strategy_name:<15} {symbol:<10} "
                          f"${result.final_balance:<11,.2f} "
                          f"${result.total_return:<11,.2f} "
                          f"{result.total_return_pct:<10.2%} "
                          f"{result.total_trades:<8} "
                          f"{result.win_rate:<8.2%} "
                          f"{result.max_drawdown:<10.2%} "
                          f"{result.sharpe_ratio:<10.2f}")

                    strategy_metrics.append({
                        'final_balance': result.final_balance,
                        'total_return': result.total_return,
                        'return_pct': result.total_return_pct,
                        'trades': result.total_trades,
                        'win_rate': result.win_rate,
                        'max_drawdown': result.max_drawdown,
                        'sharpe_ratio': result.sharpe_ratio
                    })

            # 计算策略平均表现
            if strategy_metrics:
                avg_final_balance = np.mean([m['final_balance'] for m in strategy_metrics])
                avg_return = np.mean([m['total_return'] for m in strategy_metrics])
                avg_return_pct = np.mean([m['return_pct'] for m in strategy_metrics])
                total_trades = sum([m['trades'] for m in strategy_metrics])
                avg_win_rate = np.mean([m['win_rate'] for m in strategy_metrics])
                avg_drawdown = np.mean([m['max_drawdown'] for m in strategy_metrics])
                avg_sharpe = np.mean([m['sharpe_ratio'] for m in strategy_metrics])

                strategy_summary[strategy_name] = {
                    'avg_final_balance': avg_final_balance,
                    'avg_return': avg_return,
                    'avg_return_pct': avg_return_pct,
                    'total_trades': total_trades,
                    'avg_win_rate': avg_win_rate,
                    'avg_drawdown': avg_drawdown,
                    'avg_sharpe': avg_sharpe
                }

        # 策略排名
        print("\n🏆 策略排名")
        print("-" * 60)

        if strategy_summary:
            # 按平均收益率排名
            sorted_by_return = sorted(strategy_summary.items(),
                                     key=lambda x: x[1]['avg_return_pct'], reverse=True)
            print("按平均收益率排名:")
            for i, (name, metrics) in enumerate(sorted_by_return, 1):
                print(f"  {i}. {name}: {metrics['avg_return_pct']:.2%} "
                      f"(${metrics['avg_return']:,.2f} USDC)")

            # 按夏普比率排名
            sorted_by_sharpe = sorted(strategy_summary.items(),
                                     key=lambda x: x[1]['avg_sharpe'], reverse=True)
            print("\n按夏普比率排名:")
            for i, (name, metrics) in enumerate(sorted_by_sharpe, 1):
                print(f"  {i}. {name}: {metrics['avg_sharpe']:.2f}")

            # 风险分析
            print("\n⚠️  风险分析")
            print("-" * 60)
            for name, metrics in strategy_summary.items():
                risk_level = "低" if metrics['avg_drawdown'] < 0.05 else "中" if metrics['avg_drawdown'] < 0.15 else "高"
                print(f"{name}: 平均最大回撤 {metrics['avg_drawdown']:.2%} (风险等级: {risk_level})")

        # 保存报告
        try:
            report_data = {
                'timestamp': datetime.now().isoformat(),
                'backtest_period': '90天',
                'initial_balance_per_strategy': self.initial_balance,
                'strategy_summary': strategy_summary,
                'detailed_results': {}
            }

            # 添加详细结果
            for strategy_name, strategy_results in all_results.items():
                report_data['detailed_results'][strategy_name] = {}
                for symbol, result in strategy_results.items():
                    if result:
                        report_data['detailed_results'][strategy_name][symbol] = {
                            'initial_balance': result.initial_balance,
                            'final_balance': result.final_balance,
                            'total_return': result.total_return,
                            'total_return_pct': result.total_return_pct,
                            'total_trades': result.total_trades,
                            'win_rate': result.win_rate,
                            'max_drawdown': result.max_drawdown,
                            'sharpe_ratio': result.sharpe_ratio
                        }

            with open('90day_backtest_report.json', 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2, default=str)

            print(f"\n📄 详细报告已保存到: 90day_backtest_report.json")

        except Exception as e:
            print(f"保存报告失败: {e}")


def run_doji_backtest():
    """运行DOJIplus策略回测"""
    print("=" * 60)
    print("DOJIplus策略回测")
    print("=" * 60)
    
    try:
        # 初始化组件
        print("初始化组件...")
        client = BinanceClient()
        data_fetcher = DataFetcher(client)
        backtester = Backtester(data_fetcher)
        
        # 创建策略
        strategy_config = config.get_strategy_config('doji_plus')
        strategy = DojiPlusStrategy(client, data_fetcher, strategy_config)
        
        print(f"策略配置: {strategy_config}")
        
        # 运行回测
        print("开始回测...")
        result = backtester.run_backtest(
            strategy=strategy,
            symbol='BTCUSDT',
            start_date='2024-01-01',
            end_date='2024-12-31',
            timeframe='1h'
        )
        
        # 显示结果
        print("\n" + "=" * 60)
        print("回测结果")
        print("=" * 60)
        
        report = backtester.generate_report(result)
        print(report)
        
        # 显示详细交易记录
        if result.trades:
            print("\n最近10笔交易:")
            print("-" * 80)
            print(f"{'时间':<20} {'方向':<6} {'价格':<10} {'数量':<12} {'盈亏':<10}")
            print("-" * 80)
            
            for trade in result.trades[-10:]:
                print(f"{trade.entry_time.strftime('%Y-%m-%d %H:%M'):<20} "
                      f"{trade.side:<6} "
                      f"{trade.entry_price:<10.2f} "
                      f"{trade.quantity:<12.6f} "
                      f"{trade.net_pnl:<10.2f}")
        
        # 保存图表
        try:
            chart_path = 'backtest_results.png'
            backtester.plot_results(result, save_path=chart_path)
            print(f"\n回测图表已保存到: {chart_path}")
        except Exception as e:
            print(f"保存图表失败: {e}")
        
        return result
        
    except Exception as e:
        print(f"回测失败: {e}")
        logger.error(f"回测失败: {e}")
        return None


def compare_strategies():
    """比较不同策略的表现"""
    print("=" * 60)
    print("策略对比回测")
    print("=" * 60)
    
    try:
        # 初始化组件
        client = BinanceClient()
        data_fetcher = DataFetcher(client)
        backtester = Backtester(data_fetcher)
        
        strategies = []
        results = []
        
        # DOJIplus策略
        doji_config = config.get_strategy_config('doji_plus')
        doji_strategy = DojiPlusStrategy(client, data_fetcher, doji_config)
        strategies.append(('DOJIplus', doji_strategy))
        
        # 可以添加更多策略进行对比
        # triangle_config = config.get_strategy_config('triangle_arbitrage')
        # triangle_strategy = TriangleArbitrageStrategy(client, data_fetcher, triangle_config)
        # strategies.append(('TriangleArbitrage', triangle_strategy))
        
        # 运行回测
        for name, strategy in strategies:
            print(f"\n正在回测策略: {name}")
            try:
                result = backtester.run_backtest(
                    strategy=strategy,
                    symbol='BTCUSDT',
                    start_date='2024-01-01',
                    end_date='2024-12-31',
                    timeframe='1h'
                )
                results.append((name, result))
                print(f"✓ {name} 回测完成")
            except Exception as e:
                print(f"✗ {name} 回测失败: {e}")
        
        # 对比结果
        if results:
            print("\n" + "=" * 80)
            print("策略对比结果")
            print("=" * 80)
            print(f"{'策略':<15} {'总收益':<12} {'收益率':<10} {'胜率':<8} {'最大回撤':<10} {'夏普比率':<10}")
            print("-" * 80)
            
            for name, result in results:
                print(f"{name:<15} "
                      f"{result.total_return:<12.2f} "
                      f"{result.total_return_pct:<10.2%} "
                      f"{result.win_rate:<8.2%} "
                      f"{result.max_drawdown:<10.2%} "
                      f"{result.sharpe_ratio:<10.2f}")
        
        return results
        
    except Exception as e:
        print(f"策略对比失败: {e}")
        return []


def main():
    """主函数"""
    print("币安量化交易系统 - 回测模块")
    print("请选择回测模式:")
    print("1. DOJIplus策略回测")
    print("2. 策略对比回测")
    print("3. 90天多策略回测 (1万USDC本金) ⭐ 推荐")
    print("4. 退出")

    while True:
        try:
            choice = input("\n请输入选择 (1-4): ").strip()

            if choice == '1':
                run_doji_backtest()
                break
            elif choice == '2':
                compare_strategies()
                break
            elif choice == '3':
                # 90天多策略回测
                print("\n启动90天多策略回测...")
                backtester = MultiStrategyBacktester()
                results = backtester.run_90day_backtest()

                if results:
                    print("\n🎉 90天回测完成！")
                    print("详细报告已保存到 90day_backtest_report.json")
                else:
                    print("\n⚠️ 回测未产生有效结果")
                break
            elif choice == '4':
                print("退出回测")
                break
            else:
                print("无效选择，请重新输入")

        except KeyboardInterrupt:
            print("\n用户中断")
            break
        except Exception as e:
            print(f"输入错误: {e}")


if __name__ == "__main__":
    main()
