#!/usr/bin/env python3
"""
多策略回测系统
同时测试DOJIplus、三角套利和网格交易三种策略，并提供详细的性能对比分析
"""

import sys
import os
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle
import seaborn as sns

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.api.binance_client import BinanceClient
from src.data.data_fetcher import DataFetcher
from src.strategies.doji_plus import DojiPlusStrategy
from src.strategies.triangle_arbitrage import TriangleArbitrageStrategy
from src.strategies.matrix_grid import MatrixGridStrategy
from src.backtest.backtester import Backtester, BacktestResult
from config.config import config
from src.utils.logger import get_logger

logger = get_logger(__name__)

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class MultiStrategyBacktester:
    """多策略回测系统"""

    def __init__(self):
        """初始化多策略回测器"""
        self.client = BinanceClient()
        self.data_fetcher = DataFetcher(self.client)
        self.backtester = Backtester(self.data_fetcher)

        # 策略配置
        self.strategies_config = {
            'DOJIplus': {
                'class': DojiPlusStrategy,
                'config': config.get_strategy_config('doji_plus'),
                'description': 'DOJIplus趋势策略 - 基于十字星形态和多技术指标'
            },
            'TriangleArbitrage': {
                'class': TriangleArbitrageStrategy,
                'config': config.get_strategy_config('triangle_arbitrage'),
                'description': '三角套利策略 - BTC/ETH/SOL三角套利'
            },
            'MatrixGrid': {
                'class': MatrixGridStrategy,
                'config': config.get_strategy_config('grid_trading'),
                'description': '网格交易策略 - 动态网格高频交易'
            }
        }

        # 回测参数
        self.backtest_params = {
            'symbols': ['BTCUSDT', 'ETHUSDT', 'SOLUSDT'],
            'timeframes': ['1h', '4h'],
            'periods': [
                ('短期', '2024-10-01', '2024-12-31'),
                ('中期', '2024-07-01', '2024-12-31'),
                ('长期', '2024-01-01', '2024-12-31')
            ]
        }

        logger.info("多策略回测系统初始化完成")

    def run_comprehensive_backtest(self) -> Dict[str, Any]:
        """运行综合回测"""
        print("=" * 80)
        print("多策略综合回测系统")
        print("=" * 80)

        all_results = {}

        for strategy_name, strategy_info in self.strategies_config.items():
            print(f"\n正在回测策略: {strategy_name}")
            print(f"描述: {strategy_info['description']}")
            print("-" * 60)

            strategy_results = {}

            # 创建策略实例
            try:
                strategy = strategy_info['class'](
                    self.client,
                    self.data_fetcher,
                    strategy_info['config']
                )

                # 对每个交易对和时间段进行回测
                for symbol in self.backtest_params['symbols']:
                    symbol_results = {}

                    for period_name, start_date, end_date in self.backtest_params['periods']:
                        print(f"  回测 {symbol} - {period_name} ({start_date} 到 {end_date})")

                        try:
                            # 选择合适的时间框架
                            timeframe = '1h' if strategy_name != 'TriangleArbitrage' else '5m'

                            result = self.backtester.run_backtest(
                                strategy=strategy,
                                symbol=symbol,
                                start_date=start_date,
                                end_date=end_date,
                                timeframe=timeframe
                            )

                            symbol_results[period_name] = result
                            print(f"    ✓ 完成 - 收益率: {result.total_return_pct:.2%}")

                        except Exception as e:
                            print(f"    ✗ 失败: {e}")
                            logger.error(f"回测失败 {strategy_name}-{symbol}-{period_name}: {e}")

                    strategy_results[symbol] = symbol_results

                all_results[strategy_name] = strategy_results
                print(f"✓ {strategy_name} 策略回测完成")

            except Exception as e:
                print(f"✗ {strategy_name} 策略初始化失败: {e}")
                logger.error(f"策略初始化失败 {strategy_name}: {e}")

        return all_results


def run_doji_backtest():
    """运行DOJIplus策略回测"""
    print("=" * 60)
    print("DOJIplus策略回测")
    print("=" * 60)
    
    try:
        # 初始化组件
        print("初始化组件...")
        client = BinanceClient()
        data_fetcher = DataFetcher(client)
        backtester = Backtester(data_fetcher)
        
        # 创建策略
        strategy_config = config.get_strategy_config('doji_plus')
        strategy = DojiPlusStrategy(client, data_fetcher, strategy_config)
        
        print(f"策略配置: {strategy_config}")
        
        # 运行回测
        print("开始回测...")
        result = backtester.run_backtest(
            strategy=strategy,
            symbol='BTCUSDT',
            start_date='2024-01-01',
            end_date='2024-12-31',
            timeframe='1h'
        )
        
        # 显示结果
        print("\n" + "=" * 60)
        print("回测结果")
        print("=" * 60)
        
        report = backtester.generate_report(result)
        print(report)
        
        # 显示详细交易记录
        if result.trades:
            print("\n最近10笔交易:")
            print("-" * 80)
            print(f"{'时间':<20} {'方向':<6} {'价格':<10} {'数量':<12} {'盈亏':<10}")
            print("-" * 80)
            
            for trade in result.trades[-10:]:
                print(f"{trade.entry_time.strftime('%Y-%m-%d %H:%M'):<20} "
                      f"{trade.side:<6} "
                      f"{trade.entry_price:<10.2f} "
                      f"{trade.quantity:<12.6f} "
                      f"{trade.net_pnl:<10.2f}")
        
        # 保存图表
        try:
            chart_path = 'backtest_results.png'
            backtester.plot_results(result, save_path=chart_path)
            print(f"\n回测图表已保存到: {chart_path}")
        except Exception as e:
            print(f"保存图表失败: {e}")
        
        return result
        
    except Exception as e:
        print(f"回测失败: {e}")
        logger.error(f"回测失败: {e}")
        return None


def compare_strategies():
    """比较不同策略的表现"""
    print("=" * 60)
    print("策略对比回测")
    print("=" * 60)
    
    try:
        # 初始化组件
        client = BinanceClient()
        data_fetcher = DataFetcher(client)
        backtester = Backtester(data_fetcher)
        
        strategies = []
        results = []
        
        # DOJIplus策略
        doji_config = config.get_strategy_config('doji_plus')
        doji_strategy = DojiPlusStrategy(client, data_fetcher, doji_config)
        strategies.append(('DOJIplus', doji_strategy))
        
        # 可以添加更多策略进行对比
        # triangle_config = config.get_strategy_config('triangle_arbitrage')
        # triangle_strategy = TriangleArbitrageStrategy(client, data_fetcher, triangle_config)
        # strategies.append(('TriangleArbitrage', triangle_strategy))
        
        # 运行回测
        for name, strategy in strategies:
            print(f"\n正在回测策略: {name}")
            try:
                result = backtester.run_backtest(
                    strategy=strategy,
                    symbol='BTCUSDT',
                    start_date='2024-01-01',
                    end_date='2024-12-31',
                    timeframe='1h'
                )
                results.append((name, result))
                print(f"✓ {name} 回测完成")
            except Exception as e:
                print(f"✗ {name} 回测失败: {e}")
        
        # 对比结果
        if results:
            print("\n" + "=" * 80)
            print("策略对比结果")
            print("=" * 80)
            print(f"{'策略':<15} {'总收益':<12} {'收益率':<10} {'胜率':<8} {'最大回撤':<10} {'夏普比率':<10}")
            print("-" * 80)
            
            for name, result in results:
                print(f"{name:<15} "
                      f"{result.total_return:<12.2f} "
                      f"{result.total_return_pct:<10.2%} "
                      f"{result.win_rate:<8.2%} "
                      f"{result.max_drawdown:<10.2%} "
                      f"{result.sharpe_ratio:<10.2f}")
        
        return results
        
    except Exception as e:
        print(f"策略对比失败: {e}")
        return []


def main():
    """主函数"""
    print("币安量化交易系统 - 回测模块")
    print("请选择回测模式:")
    print("1. DOJIplus策略回测")
    print("2. 策略对比回测")
    print("3. 退出")
    
    while True:
        try:
            choice = input("\n请输入选择 (1-3): ").strip()
            
            if choice == '1':
                run_doji_backtest()
                break
            elif choice == '2':
                compare_strategies()
                break
            elif choice == '3':
                print("退出回测")
                break
            else:
                print("无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n用户中断")
            break
        except Exception as e:
            print(f"输入错误: {e}")


if __name__ == "__main__":
    main()
